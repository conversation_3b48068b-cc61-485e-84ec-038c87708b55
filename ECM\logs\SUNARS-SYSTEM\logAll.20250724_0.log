2025-07-24 00:04:48.476 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 00:09:48.479 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 08:19:57.578 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 08:24:57.588 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 08:29:57.602 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 08:34:57.611 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 08:39:57.621 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 08:44:57.635 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 08:49:57.645 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 08:54:57.654 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 08:59:57.668 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 09:04:57.682 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 09:09:57.687 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 09:14:57.699 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 09:19:57.713 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 09:24:57.728 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 09:29:57.735 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 09:34:57.737 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 09:39:57.748 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 09:44:57.758 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 09:49:57.763 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 09:54:57.764 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 09:59:57.777 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 10:04:57.789 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 10:09:57.791 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 10:14:57.793 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 10:19:57.800 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 10:24:57.813 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 10:29:57.822 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 10:34:57.827 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 10:39:57.835 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 10:44:57.845 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 10:49:57.858 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 10:54:57.860 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 10:59:57.861 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 11:04:57.863 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 11:09:57.864 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 11:14:57.865 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 11:19:57.866 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 11:24:57.868 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 11:29:57.869 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 11:34:57.870 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 11:39:57.873 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 11:44:57.875 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 11:49:57.882 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 11:54:57.886 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 11:59:57.901 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 12:04:57.909 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 12:09:57.920 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 12:14:57.935 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 12:19:57.940 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 12:24:57.951 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 12:29:57.954 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 12:34:57.959 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 12:39:57.972 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 12:44:57.979 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 12:49:57.993 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 12:54:58.001 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 12:59:58.009 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 13:04:58.023 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 13:09:58.034 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 13:14:58.039 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 13:19:58.039 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 13:24:58.055 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 13:29:58.057 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 13:34:58.060 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 13:39:58.070 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 13:44:58.071 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 13:49:58.083 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 13:54:58.088 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 13:59:58.095 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 14:04:58.103 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 14:09:58.111 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 14:14:58.121 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 14:19:58.122 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 14:24:58.124 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 14:29:58.136 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 14:34:58.151 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 14:39:58.159 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 14:44:58.164 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 14:49:58.167 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 14:54:58.167 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 14:59:58.181 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 15:04:58.197 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 15:09:58.198 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 15:14:58.206 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 15:19:58.245 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 15:24:58.251 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 15:29:58.263 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 15:34:58.271 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 15:39:58.272 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 15:43:29.489 [] [3a55e5e9c89f1159/366546fb3a0ab8e1] [http-nio-9902-exec-68] DEBUG c.s.a.s.d.b.T.selectBySelective - ==>  Preparing: select TELLER_NO, TELLER_NAME, TELLER_LEVEL, TELLER_TYPE, PARENT_ORGAN, TELLER_STATE from SM_TELLER_TB WHERE TELLER_NO = ?
2025-07-24 15:43:29.491 [] [3a55e5e9c89f1159/366546fb3a0ab8e1] [http-nio-9902-exec-68] DEBUG c.s.a.s.d.b.T.selectBySelective - ==> Parameters: admin(String)
2025-07-24 15:43:29.498 [] [3a55e5e9c89f1159/366546fb3a0ab8e1] [http-nio-9902-exec-68] DEBUG c.s.a.s.d.b.T.selectBySelective - <==      Total: 1
2025-07-24 15:43:39.801 [OrganNo_00023_UserNo_admin] [3ad0d769763522cd/9b481d4d2615d62c] [http-nio-9902-exec-66] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"organ_level":2
		}
	],
	"sysMap":{
		"oper_type":"getThisLevelOrganLisr"
	}
}
2025-07-24 15:43:39.809 [OrganNo_00023_UserNo_admin] [3ad0d769763522cd/4d51f23202f28e9b] [http-nio-9902-exec-66] DEBUG c.s.a.s.d.b.A.getCenterOrganLevel - ==>  Preparing: select parent_organ as "parent_organ" from sm_organ_parent_tb where organ_level <= '2' and organ_no=? order by organ_level desc
2025-07-24 15:43:39.810 [OrganNo_00023_UserNo_admin] [3ad0d769763522cd/4d51f23202f28e9b] [http-nio-9902-exec-66] DEBUG c.s.a.s.d.b.A.getCenterOrganLevel - ==> Parameters: 00023(String)
2025-07-24 15:43:39.814 [OrganNo_00023_UserNo_admin] [3ad0d769763522cd/4d51f23202f28e9b] [http-nio-9902-exec-66] DEBUG c.s.a.s.d.b.A.getCenterOrganLevel - <==      Total: 1
2025-07-24 15:43:39.814 [OrganNo_00023_UserNo_admin] [3ad0d769763522cd/4d51f23202f28e9b] [http-nio-9902-exec-66] DEBUG c.s.a.s.d.b.A.getThisLevelOrganLisr - ==>  Preparing: SELECT P.organ_no as "organNo" FROM SM_ORGAN_PARENT_TB P where P.PARENT_ORGAN = ? ORDER BY P.ORGAN_LEVEL
2025-07-24 15:43:39.814 [OrganNo_00023_UserNo_admin] [3ad0d769763522cd/4d51f23202f28e9b] [http-nio-9902-exec-66] DEBUG c.s.a.s.d.b.A.getThisLevelOrganLisr - ==> Parameters: 00023(String)
2025-07-24 15:43:39.818 [OrganNo_00023_UserNo_admin] [3ad0d769763522cd/4d51f23202f28e9b] [http-nio-9902-exec-66] DEBUG c.s.a.s.d.b.A.getThisLevelOrganLisr - <==      Total: 8
2025-07-24 15:43:39.916 [OrganNo_00023_UserNo_admin] [3ad0d769763522cd/9b481d4d2615d62c] [http-nio-9902-exec-66] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"branchs":[
			{
				"organName":"中国移动银行",
				"organNo":"100861",
				"organLevel":2
			},
			{
				"organName":"四个九",
				"organNo":"9999",
				"organLevel":2
			},
			{
				"organName":"测试周一",
				"organNo":"1101",
				"organLevel":2
			},
			{
				"organName":"中国银行四川省分行",
				"organNo":"00023",
				"organLevel":1
			},
			{
				"organName":"成都地区",
				"organNo":"5100",
				"organLevel":2
			},
			{
				"organName":"省行本部",
				"organNo":"5102",
				"organLevel":2
			},
			{
				"organName":"其他地区",
				"organNo":"5101",
				"organLevel":2
			}
		]
	},
	"retMsg":"查询成功"
}
2025-07-24 15:43:39.917 [] [3ad0d769763522cd/9b481d4d2615d62c] [http-nio-9902-exec-66] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 操作机构信息 操作方法: 查询指定指定级别机构!请求IP地址: ************** 操作开始时间: 2025-07-24 15:43:39 操作结束时间: 2025-07-24 15:43:39!总共花费时间: 127 毫秒！
2025-07-24 15:43:40.289 [] [097834f31b9ef843/61bcf6511b16958b] [http-nio-9902-exec-67] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 操作机构信息 操作方法: 用户权限机构!请求IP地址: ************** 操作开始时间: 2025-07-24 15:43:40 操作结束时间: 2025-07-24 15:43:40!总共花费时间: 223 毫秒！
2025-07-24 15:43:40.468 [] [1adf0a9b081060b0/b79352d4e411d8c8] [http-nio-9902-exec-70] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 操作机构信息 操作方法: 用户权限机构!请求IP地址: ************** 操作开始时间: 2025-07-24 15:43:40 操作结束时间: 2025-07-24 15:43:40!总共花费时间: 144 毫秒！
2025-07-24 15:44:58.286 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 15:49:58.294 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 15:54:58.301 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 15:59:58.303 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 16:04:58.307 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 16:09:58.315 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 16:12:32.793 [] [379d20702817872b/f59fc2e09ef2fdaa] [http-nio-9902-exec-72] DEBUG c.s.a.c.d.S.getFieldsByTableId - ==>  Preparing: select T1.FIELD_NAME "FIELD_NAME",T2.ELSE_NAME "ELSE_NAME",T2.FIELD_FLOAT "FIELD_FLOAT" from sm_table_field_tb t1,sm_field_def_tb t2 where t1.field_name=t2.field_name and t1.table_id = ? and t1.location!=0 order by t1.location
2025-07-24 16:12:32.794 [] [379d20702817872b/f59fc2e09ef2fdaa] [http-nio-9902-exec-72] DEBUG c.s.a.c.d.S.getFieldsByTableId - ==> Parameters: 3(Integer)
2025-07-24 16:12:32.797 [] [379d20702817872b/f59fc2e09ef2fdaa] [http-nio-9902-exec-72] DEBUG c.s.a.c.d.S.getFieldsByTableId - <==      Total: 19
2025-07-24 16:13:05.754 [] [ee0fb50ed0f175b0/9e00f548ddf951bd] [http-nio-9902-exec-74] DEBUG c.s.a.c.d.S.getFieldsByTableId - ==>  Preparing: select T1.FIELD_NAME "FIELD_NAME",T2.ELSE_NAME "ELSE_NAME",T2.FIELD_FLOAT "FIELD_FLOAT" from sm_table_field_tb t1,sm_field_def_tb t2 where t1.field_name=t2.field_name and t1.table_id = ? and t1.location!=0 order by t1.location
2025-07-24 16:13:05.756 [] [ee0fb50ed0f175b0/9e00f548ddf951bd] [http-nio-9902-exec-74] DEBUG c.s.a.c.d.S.getFieldsByTableId - ==> Parameters: 3(Integer)
2025-07-24 16:13:05.760 [] [ee0fb50ed0f175b0/9e00f548ddf951bd] [http-nio-9902-exec-74] DEBUG c.s.a.c.d.S.getFieldsByTableId - <==      Total: 19
2025-07-24 16:13:06.328 [] [66ed12056f559406/1fd00f421bd1056f] [http-nio-9902-exec-79] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select DATA_SOURCE_ID, GROUP_NAME, MODE_CODE, INDEX_NAME, FILE_PART_NAME, UA_IP, UA_PORT, DATA_DESC, CUST_ID, SERVICE_ID,DATA_SOURCE_TYPE from SM_DATASOURCE_SET_TB where DATA_SOURCE_ID = ?
2025-07-24 16:13:06.329 [] [66ed12056f559406/1fd00f421bd1056f] [http-nio-9902-exec-79] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 0(String)
2025-07-24 16:13:06.333 [] [66ed12056f559406/1fd00f421bd1056f] [http-nio-9902-exec-79] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:13:06.348 [] [66ed12056f559406/70a8bc2cd76ba441] [http-nio-9902-exec-76] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select SERVICE_ID, SERVICE_NAME, SERVICE_IP, SERVICE_PORT, SERVICE_TYPE, LOGIN_NAME, LOGIN_PASS, IS_MONITOR, LISTENER_POST, GROUP_ID, WORK_TYPE from SM_SERVICE_REG_TB where SERVICE_ID = ?
2025-07-24 16:13:06.350 [] [66ed12056f559406/70a8bc2cd76ba441] [http-nio-9902-exec-76] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 1(BigDecimal)
2025-07-24 16:13:06.356 [] [66ed12056f559406/70a8bc2cd76ba441] [http-nio-9902-exec-76] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:07.113 [] [731cf6b6674f1e71/dc1fb34ab94ce4e9] [http-nio-9902-exec-78] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select DATA_SOURCE_ID, GROUP_NAME, MODE_CODE, INDEX_NAME, FILE_PART_NAME, UA_IP, UA_PORT, DATA_DESC, CUST_ID, SERVICE_ID,DATA_SOURCE_TYPE from SM_DATASOURCE_SET_TB where DATA_SOURCE_ID = ?
2025-07-24 16:14:07.114 [] [731cf6b6674f1e71/dc1fb34ab94ce4e9] [http-nio-9902-exec-78] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 0(String)
2025-07-24 16:14:07.117 [] [731cf6b6674f1e71/dc1fb34ab94ce4e9] [http-nio-9902-exec-78] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:07.124 [] [731cf6b6674f1e71/6f6abd5dd3f88c39] [http-nio-9902-exec-82] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select SERVICE_ID, SERVICE_NAME, SERVICE_IP, SERVICE_PORT, SERVICE_TYPE, LOGIN_NAME, LOGIN_PASS, IS_MONITOR, LISTENER_POST, GROUP_ID, WORK_TYPE from SM_SERVICE_REG_TB where SERVICE_ID = ?
2025-07-24 16:14:07.125 [] [731cf6b6674f1e71/6f6abd5dd3f88c39] [http-nio-9902-exec-82] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 1(BigDecimal)
2025-07-24 16:14:07.127 [] [731cf6b6674f1e71/6f6abd5dd3f88c39] [http-nio-9902-exec-82] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:07.918 [] [63e36fe03f45f4f7/d305ee19443e15bc] [http-nio-9902-exec-80] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select DATA_SOURCE_ID, GROUP_NAME, MODE_CODE, INDEX_NAME, FILE_PART_NAME, UA_IP, UA_PORT, DATA_DESC, CUST_ID, SERVICE_ID,DATA_SOURCE_TYPE from SM_DATASOURCE_SET_TB where DATA_SOURCE_ID = ?
2025-07-24 16:14:07.920 [] [63e36fe03f45f4f7/d305ee19443e15bc] [http-nio-9902-exec-80] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 0(String)
2025-07-24 16:14:07.922 [] [63e36fe03f45f4f7/d305ee19443e15bc] [http-nio-9902-exec-80] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:07.929 [] [63e36fe03f45f4f7/476844ccbae1a6c6] [http-nio-9902-exec-84] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select SERVICE_ID, SERVICE_NAME, SERVICE_IP, SERVICE_PORT, SERVICE_TYPE, LOGIN_NAME, LOGIN_PASS, IS_MONITOR, LISTENER_POST, GROUP_ID, WORK_TYPE from SM_SERVICE_REG_TB where SERVICE_ID = ?
2025-07-24 16:14:07.929 [] [63e36fe03f45f4f7/476844ccbae1a6c6] [http-nio-9902-exec-84] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 1(BigDecimal)
2025-07-24 16:14:07.932 [] [63e36fe03f45f4f7/476844ccbae1a6c6] [http-nio-9902-exec-84] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:08.559 [] [87acf394bf78ba88/1bf14cfced9d9d12] [http-nio-9902-exec-75] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select DATA_SOURCE_ID, GROUP_NAME, MODE_CODE, INDEX_NAME, FILE_PART_NAME, UA_IP, UA_PORT, DATA_DESC, CUST_ID, SERVICE_ID,DATA_SOURCE_TYPE from SM_DATASOURCE_SET_TB where DATA_SOURCE_ID = ?
2025-07-24 16:14:08.560 [] [87acf394bf78ba88/1bf14cfced9d9d12] [http-nio-9902-exec-75] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 0(String)
2025-07-24 16:14:08.564 [] [87acf394bf78ba88/1bf14cfced9d9d12] [http-nio-9902-exec-75] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:08.571 [] [87acf394bf78ba88/ac988d1c7b58baba] [http-nio-9902-exec-83] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select SERVICE_ID, SERVICE_NAME, SERVICE_IP, SERVICE_PORT, SERVICE_TYPE, LOGIN_NAME, LOGIN_PASS, IS_MONITOR, LISTENER_POST, GROUP_ID, WORK_TYPE from SM_SERVICE_REG_TB where SERVICE_ID = ?
2025-07-24 16:14:08.571 [] [87acf394bf78ba88/ac988d1c7b58baba] [http-nio-9902-exec-83] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 1(BigDecimal)
2025-07-24 16:14:08.574 [] [87acf394bf78ba88/ac988d1c7b58baba] [http-nio-9902-exec-83] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:09.190 [] [19c71d8414b9aa3f/6b3d5ff40164fc64] [http-nio-9902-exec-81] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select DATA_SOURCE_ID, GROUP_NAME, MODE_CODE, INDEX_NAME, FILE_PART_NAME, UA_IP, UA_PORT, DATA_DESC, CUST_ID, SERVICE_ID,DATA_SOURCE_TYPE from SM_DATASOURCE_SET_TB where DATA_SOURCE_ID = ?
2025-07-24 16:14:09.190 [] [19c71d8414b9aa3f/6b3d5ff40164fc64] [http-nio-9902-exec-81] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 0(String)
2025-07-24 16:14:09.193 [] [19c71d8414b9aa3f/6b3d5ff40164fc64] [http-nio-9902-exec-81] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:09.199 [] [19c71d8414b9aa3f/7d3f033517f76843] [http-nio-9902-exec-85] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select SERVICE_ID, SERVICE_NAME, SERVICE_IP, SERVICE_PORT, SERVICE_TYPE, LOGIN_NAME, LOGIN_PASS, IS_MONITOR, LISTENER_POST, GROUP_ID, WORK_TYPE from SM_SERVICE_REG_TB where SERVICE_ID = ?
2025-07-24 16:14:09.200 [] [19c71d8414b9aa3f/7d3f033517f76843] [http-nio-9902-exec-85] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 1(BigDecimal)
2025-07-24 16:14:09.203 [] [19c71d8414b9aa3f/7d3f033517f76843] [http-nio-9902-exec-85] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
