2025-07-24 15:43:41.089 [OrganNo_00023_UserNo_admin] [feff2c01d62255cc/3a1bd4a966234a95] [http-nio-9060-exec-1] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"getAllModelInfos"
	}
}
2025-07-24 15:43:41.199 [OrganNo_00023_UserNo_admin] [feff2c01d62255cc/3a1bd4a966234a95] [http-nio-9060-exec-1] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"modelList":[
			{
				"relating_model_id":1,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1001-错账冲正交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":2,
				"model_check_way":"0",
				"table_name":"ZD_1001_CUOZCZJY",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1001-错账冲正交易(明细)",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":1,
				"model_check_way":"0",
				"table_name":"ZD_1001_CUOZCZJY",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":3,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1002-账户冻结解冻交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":4,
				"model_check_way":"0",
				"table_name":"ZD_1002_ZHANGHDJJD",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1002-账户冻结解冻交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":3,
				"model_check_way":"0",
				"table_name":"ZD_1002_ZHANGHDJJD",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":5,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1003-账户挂失、解挂交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":6,
				"model_check_way":"0",
				"table_name":"ZD_1003_ZHANGHGSJG",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1003-账户挂失、解挂交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":5,
				"model_check_way":"0",
				"table_name":"ZD_1003_ZHANGHGSJG",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":7,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1004-特殊汇率交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":8,
				"model_check_way":"0",
				"table_name":"ZD_1004_TESHL",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1004-特殊汇率交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":7,
				"model_check_way":"0",
				"table_name":"ZD_1004_TESHL",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":9,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1005-强制支取",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":10,
				"model_check_way":"0",
				"table_name":"ZD_1005_QIANGZZQ",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1005-强制支取（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":9,
				"model_check_way":"0",
				"table_name":"ZD_1005_QIANGZZQ",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":11,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1006-非当日起息交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":12,
				"model_check_way":"0",
				"table_name":"ZD_1006_FEIDRQX",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1006-非当日起息交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":11,
				"model_check_way":"0",
				"table_name":"ZD_1006_FEIDRQX",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":13,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1007-存、贷款调整交易、手工强制结息交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":14,
				"model_check_way":"0",
				"table_name":"ZD_1007_CUNDKTZSGQZJX",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1007-存、贷款调整交易、手工强制结息交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":13,
				"model_check_way":"0",
				"table_name":"ZD_1007_CUNDKTZSGQZJX",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":15,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1008-存款账户利率维护/变更交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":16,
				"model_check_way":"0",
				"table_name":"ZD_1008_CUNKZHLLWH",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1008-存款账户利率维护/变更交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":15,
				"model_check_way":"0",
				"table_name":"ZD_1008_CUNKZHLLWH",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":17,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1009-存款、贷款账户换机构交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":18,
				"model_check_way":"0",
				"table_name":"ZD_1009_CUNKDKZHHJG",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1009-存款、贷款账户换机构交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":17,
				"model_check_way":"0",
				"table_name":"ZD_1009_CUNKDKZHHJG",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":19,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1010-重空状态变更交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":20,
				"model_check_way":"0",
				"table_name":"ZD_1010_ZHONGKZTBG",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1010-重空状态变更交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":19,
				"model_check_way":"0",
				"table_name":"ZD_1010_ZHONGKZTBG",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":21,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1011-5111人民币长期不动户支取",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":22,
				"model_check_way":"0",
				"table_name":"ZD_1011_CHANGQWJFHKJK",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1011-5111人民币长期不动户支取（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":21,
				"model_check_way":"0",
				"table_name":"ZD_1011_CHANGQWJFHKJK",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":23,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1012-大额手续费收入及支出检查",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":24,
				"model_check_way":"0",
				"table_name":"ZD_1012_SHOUXFSRJC",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1012-大额手续费收入及支出检查（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":23,
				"model_check_way":"0",
				"table_name":"ZD_1012_SHOUXFSRJC",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":25,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1013-741挂账、842销账、8429支取交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":26,
				"model_check_way":"0",
				"table_name":"ZD_1013_GUAZXZZQ",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1013-741挂账、842销账、8429支取交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":25,
				"model_check_way":"0",
				"table_name":"ZD_1013_GUAZXZZQ",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":27,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1014-单位大额交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":28,
				"model_check_way":"0",
				"table_name":"ZD_1014_DANWDEJY",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1014-单位大额交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":27,
				"model_check_way":"0",
				"table_name":"ZD_1014_DANWDEJY",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":29,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1015-不同客户新开立借记卡预留相同手机号",
				"is_relate_model":0,
				"model_type":"0",
				"model_id":30,
				"model_check_way":"0",
				"table_name":"YJ_1001_XINKDZHSJXT",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1015-不同客户新开立借记卡预留相同手机号（明细）",
				"is_relate_model":1,
				"model_type":"0",
				"model_id":29,
				"model_check_way":"0",
				"table_name":"YJ_1001_XINKDZHSJXT",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":31,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1016-验资（增资）账户冻结情况检查",
				"is_relate_model":0,
				"model_type":"0",
				"model_id":32,
				"model_check_way":"0",
				"table_name":"YJ_1002_YANZZZZH",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1016-验资（增资）账户冻结情况检查（明细）",
				"is_relate_model":1,
				"model_type":"0",
				"model_id":31,
				"model_check_way":"0",
				"table_name":"YJ_1002_YANZZZZH",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":33,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1017-非营业时间（19时后）交易",
				"is_relate_model":0,
				"model_type":"0",
				"model_id":34,
				"model_check_way":"0",
				"table_name":"YJ_1003_FEIYYSJJY",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1017-非营业时间（19时后）交易（明细）",
				"is_relate_model":1,
				"model_type":"0",
				"model_id":33,
				"model_check_way":"0",
				"table_name":"YJ_1003_FEIYYSJJY",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":36,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1018-经集中核准的人民币大额交易 ",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":35,
				"model_check_way":"0",
				"table_name":"HZ_1001_RMBDEJY",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBI",
				"model_name":"1018-经集中核准的人民币大额交易(明细)  ",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":36,
				"model_check_way":"0",
				"table_name":"HZ_1001_RMBDEJY",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":38,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1019-经集中核准的外币大额交易 ",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":37,
				"model_check_way":"0",
				"table_name":"HZ_1002_WBDEJY",
				"model_data_check_way":"1"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1019-经集中核准的外币大额交易(明细)",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":38,
				"model_check_way":"0",
				"table_name":"HZ_1002_WBDEJY",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":40,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1020-经集中核准的司法扣划交易 ",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":39,
				"model_check_way":"0",
				"table_name":"HZ_1003_SFKHJY",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBI",
				"model_name":"1020-经集中核准的司法扣划交易(明细)",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":40,
				"model_check_way":"0",
				"table_name":"HZ_1003_SFKHJY",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":42,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1021-经集中核准的司法冻结、解冻交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":41,
				"model_check_way":"0",
				"table_name":"HZ_1004_SFDJJDJY",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1021-经集中核准的司法冻结、解冻交易(明细)",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":42,
				"model_check_way":"0",
				"table_name":"HZ_1004_SFDJJDJY",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":44,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1022-经集中核准的挂失、解挂交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":43,
				"model_check_way":"0",
				"table_name":"HZ_1005_GSJGJY",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1022-经集中核准的挂失、解挂交易(明细) ",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":44,
				"model_check_way":"0",
				"table_name":"HZ_1005_GSJGJY",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":46,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1023-存款利率调整成功模型",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":45,
				"model_check_way":"0",
				"table_name":"CSAR7010",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1023-存款利率调整成功模型(明细) ",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":46,
				"model_check_way":"0",
				"table_name":"CSAR7010",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":48,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1024-贷款利率调整成功模型",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":47,
				"model_check_way":"0",
				"table_name":"CSAR7020",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1024-贷款利率调整成功模型(明细) ",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":48,
				"model_check_way":"0",
				"table_name":"CSAR7020",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":50,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1025-特殊业务调整成功模型",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":49,
				"model_check_way":"0",
				"table_name":"CSAR7040",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1025-特殊业务调整成功模型(明细) ",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":50,
				"model_check_way":"0",
				"table_name":"CSAR7040",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":52,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1026-GLIF调整成功模型",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":51,
				"model_check_way":"0",
				"table_name":"CSAR7050",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1026-GLIF调整成功模型(明细) ",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":52,
				"model_check_way":"0",
				"table_name":"CSAR7050",
				"model_data_check_way":"0"
			}
		]
	},
	"retMsg":"查询成功"
}
2025-07-24 15:43:41.204 [] [feff2c01d62255cc/3a1bd4a966234a95] [http-nio-9060-exec-1] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警模型列表!请求IP地址: ************** 操作开始时间: 2025-07-24 15:43:41 操作结束时间: 2025-07-24 15:43:41!总共花费时间: 184 毫秒！
2025-07-24 15:43:42.248 [OrganNo_00023_UserNo_admin] [906c02123d3a64bc/f4bf893bf74bf70c] [http-nio-9060-exec-2] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"end_date":"20250723",
			"model_id":"",
			"site_no":"",
			"start_date":"20250623"
		}
	],
	"sysMap":{
		
	}
}
2025-07-24 15:43:42.386 [OrganNo_00023_UserNo_admin] [906c02123d3a64bc/f4bf893bf74bf70c] [http-nio-9060-exec-2] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"riskWarningStats":[
			
		]
	},
	"retMsg":"查询成功"
}
2025-07-24 15:43:42.387 [] [906c02123d3a64bc/f4bf893bf74bf70c] [http-nio-9060-exec-2] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 查询预警信息统计数据!请求IP地址: ************** 操作开始时间: 2025-07-24 15:43:42 操作结束时间: 2025-07-24 15:43:42!总共花费时间: 164 毫秒！
