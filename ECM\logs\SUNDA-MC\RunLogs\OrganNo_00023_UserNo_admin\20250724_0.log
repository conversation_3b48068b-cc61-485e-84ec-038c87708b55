2025-07-24 15:43:39.564 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/526afed198fcb024] [http-nio-9058-exec-58] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"businessGrade":0,
			"businessNo":0,
			"parentBusiness":"200001"
		}
	],
	"sysMap":{
		"operType":"OP004"
	}
}
2025-07-24 15:43:39.585 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] INFO  c.s.a.m.b.s.i.ModelBusinessLineTreeServiceImpl - 加载业务条线树，开始
2025-07-24 15:43:39.589 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectBusinessLineByParent - ==>  Preparing: select BUSINESS_ID as "BUSINESS_ID", BUSINESS_NAME as "BUSINESS_NAME", BUSINESS_DESC as "BUSINESS_DESC", BUSINESS_GRADE as "BUSINESS_GRADE", PARENT_BUSINESS as "PARENT_BUSINESS", BUSINESS_NO as "BUSINESS_NO" from MD_BUSINESS_LINE_TB where parent_business = ? order by business_no
2025-07-24 15:43:39.590 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectBusinessLineByParent - ==> Parameters: 200001(String)
2025-07-24 15:43:39.595 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectBusinessLineByParent - <==      Total: 9
2025-07-24 15:43:39.597 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectSonBusinessLineCount - ==>  Preparing: select max(t.num) totalNum from (select count(*) num from MD_BUSINESS_PARENT_TB where parent_businessid in ( ? ) group by parent_businessid) t
2025-07-24 15:43:39.597 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectSonBusinessLineCount - ==> Parameters: WYLB00001(String)
2025-07-24 15:43:39.599 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectSonBusinessLineCount - <==      Total: 1
2025-07-24 15:43:39.600 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectSonBusinessLineCount - ==>  Preparing: select max(t.num) totalNum from (select count(*) num from MD_BUSINESS_PARENT_TB where parent_businessid in ( ? ) group by parent_businessid) t
2025-07-24 15:43:39.601 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectSonBusinessLineCount - ==> Parameters: WYLB00002(String)
2025-07-24 15:43:39.603 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectSonBusinessLineCount - <==      Total: 1
2025-07-24 15:43:39.604 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectSonBusinessLineCount - ==>  Preparing: select max(t.num) totalNum from (select count(*) num from MD_BUSINESS_PARENT_TB where parent_businessid in ( ? ) group by parent_businessid) t
2025-07-24 15:43:39.604 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectSonBusinessLineCount - ==> Parameters: WYLB00003(String)
2025-07-24 15:43:39.607 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectSonBusinessLineCount - <==      Total: 1
2025-07-24 15:43:39.608 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectSonBusinessLineCount - ==>  Preparing: select max(t.num) totalNum from (select count(*) num from MD_BUSINESS_PARENT_TB where parent_businessid in ( ? ) group by parent_businessid) t
2025-07-24 15:43:39.608 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectSonBusinessLineCount - ==> Parameters: WYLB00004(String)
2025-07-24 15:43:39.610 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectSonBusinessLineCount - <==      Total: 1
2025-07-24 15:43:39.611 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectSonBusinessLineCount - ==>  Preparing: select max(t.num) totalNum from (select count(*) num from MD_BUSINESS_PARENT_TB where parent_businessid in ( ? ) group by parent_businessid) t
2025-07-24 15:43:39.611 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectSonBusinessLineCount - ==> Parameters: WYLB00005(String)
2025-07-24 15:43:39.612 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectSonBusinessLineCount - <==      Total: 1
2025-07-24 15:43:39.614 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectSonBusinessLineCount - ==>  Preparing: select max(t.num) totalNum from (select count(*) num from MD_BUSINESS_PARENT_TB where parent_businessid in ( ? ) group by parent_businessid) t
2025-07-24 15:43:39.614 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectSonBusinessLineCount - ==> Parameters: WYLB00006(String)
2025-07-24 15:43:39.616 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectSonBusinessLineCount - <==      Total: 1
2025-07-24 15:43:39.617 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectSonBusinessLineCount - ==>  Preparing: select max(t.num) totalNum from (select count(*) num from MD_BUSINESS_PARENT_TB where parent_businessid in ( ? ) group by parent_businessid) t
2025-07-24 15:43:39.617 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectSonBusinessLineCount - ==> Parameters: WYLB00007(String)
2025-07-24 15:43:39.618 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectSonBusinessLineCount - <==      Total: 1
2025-07-24 15:43:39.619 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectSonBusinessLineCount - ==>  Preparing: select max(t.num) totalNum from (select count(*) num from MD_BUSINESS_PARENT_TB where parent_businessid in ( ? ) group by parent_businessid) t
2025-07-24 15:43:39.619 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectSonBusinessLineCount - ==> Parameters: WYLB00008(String)
2025-07-24 15:43:39.620 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectSonBusinessLineCount - <==      Total: 1
2025-07-24 15:43:39.621 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectSonBusinessLineCount - ==>  Preparing: select max(t.num) totalNum from (select count(*) num from MD_BUSINESS_PARENT_TB where parent_businessid in ( ? ) group by parent_businessid) t
2025-07-24 15:43:39.622 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectSonBusinessLineCount - ==> Parameters: WYLB00009(String)
2025-07-24 15:43:39.623 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectSonBusinessLineCount - <==      Total: 1
2025-07-24 15:43:39.623 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] INFO  c.s.a.m.b.s.i.ModelBusinessLineTreeServiceImpl - 添加是否为父节点信息，开始
2025-07-24 15:43:39.624 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectCount - ==>  Preparing: select max(t.num) totalNum from (select count(*) num from MD_BUSINESS_PARENT_TB where PARENT_BUSINESSID in ( ? ) group by PARENT_BUSINESSID) t
2025-07-24 15:43:39.624 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectCount - ==> Parameters: WYLB00001(String)
2025-07-24 15:43:39.626 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectCount - <==      Total: 1
2025-07-24 15:43:39.627 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectCount - ==>  Preparing: select max(t.num) totalNum from (select count(*) num from MD_BUSINESS_PARENT_TB where PARENT_BUSINESSID in ( ? ) group by PARENT_BUSINESSID) t
2025-07-24 15:43:39.627 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectCount - ==> Parameters: WYLB00002(String)
2025-07-24 15:43:39.628 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectCount - <==      Total: 1
2025-07-24 15:43:39.629 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectCount - ==>  Preparing: select max(t.num) totalNum from (select count(*) num from MD_BUSINESS_PARENT_TB where PARENT_BUSINESSID in ( ? ) group by PARENT_BUSINESSID) t
2025-07-24 15:43:39.630 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectCount - ==> Parameters: WYLB00003(String)
2025-07-24 15:43:39.630 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectCount - <==      Total: 1
2025-07-24 15:43:39.631 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectCount - ==>  Preparing: select max(t.num) totalNum from (select count(*) num from MD_BUSINESS_PARENT_TB where PARENT_BUSINESSID in ( ? ) group by PARENT_BUSINESSID) t
2025-07-24 15:43:39.631 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectCount - ==> Parameters: WYLB00004(String)
2025-07-24 15:43:39.634 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectCount - <==      Total: 1
2025-07-24 15:43:39.634 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectCount - ==>  Preparing: select max(t.num) totalNum from (select count(*) num from MD_BUSINESS_PARENT_TB where PARENT_BUSINESSID in ( ? ) group by PARENT_BUSINESSID) t
2025-07-24 15:43:39.636 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectCount - ==> Parameters: WYLB00005(String)
2025-07-24 15:43:39.637 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectCount - <==      Total: 1
2025-07-24 15:43:39.639 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectCount - ==>  Preparing: select max(t.num) totalNum from (select count(*) num from MD_BUSINESS_PARENT_TB where PARENT_BUSINESSID in ( ? ) group by PARENT_BUSINESSID) t
2025-07-24 15:43:39.640 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectCount - ==> Parameters: WYLB00006(String)
2025-07-24 15:43:39.642 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectCount - <==      Total: 1
2025-07-24 15:43:39.645 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectCount - ==>  Preparing: select max(t.num) totalNum from (select count(*) num from MD_BUSINESS_PARENT_TB where PARENT_BUSINESSID in ( ? ) group by PARENT_BUSINESSID) t
2025-07-24 15:43:39.645 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectCount - ==> Parameters: WYLB00007(String)
2025-07-24 15:43:39.648 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectCount - <==      Total: 1
2025-07-24 15:43:39.648 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectCount - ==>  Preparing: select max(t.num) totalNum from (select count(*) num from MD_BUSINESS_PARENT_TB where PARENT_BUSINESSID in ( ? ) group by PARENT_BUSINESSID) t
2025-07-24 15:43:39.649 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectCount - ==> Parameters: WYLB00008(String)
2025-07-24 15:43:39.651 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectCount - <==      Total: 1
2025-07-24 15:43:39.652 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectCount - ==>  Preparing: select max(t.num) totalNum from (select count(*) num from MD_BUSINESS_PARENT_TB where PARENT_BUSINESSID in ( ? ) group by PARENT_BUSINESSID) t
2025-07-24 15:43:39.653 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectCount - ==> Parameters: WYLB00009(String)
2025-07-24 15:43:39.654 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] DEBUG c.s.a.m.b.d.M.selectCount - <==      Total: 1
2025-07-24 15:43:39.655 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] INFO  c.s.a.m.b.s.i.ModelBusinessLineTreeServiceImpl - 添加是否为父节点信息，结束，返回[{BUSINESS_ID=WYLB00001, BUSINESS_NAME=结算账户管理, BUSINESS_DESC=结算账户管理, PARENT_BUSINESS=200001, PARENT=false, BUSINESS_GRADE=2, BUSINESS_NO=3}, {BUSINESS_ID=WYLB00002, BUSINESS_NAME=内部账户及科目管理, BUSINESS_DESC=内部账户及科目管理, PARENT_BUSINESS=200001, PARENT=false, BUSINESS_GRADE=2, BUSINESS_NO=4}, {BUSINESS_ID=WYLB00003, BUSINESS_NAME=支付结算业务管理, BUSINESS_DESC=支付结算业务管理, PARENT_BUSINESS=200001, PARENT=false, BUSINESS_GRADE=2, BUSINESS_NO=5}, {BUSINESS_ID=WYLB00004, BUSINESS_NAME=信贷业务管理, BUSINESS_DESC=信贷业务管理, PARENT_BUSINESS=200001, PARENT=false, BUSINESS_GRADE=2, BUSINESS_NO=6}, {BUSINESS_ID=WYLB00005, BUSINESS_NAME=现金及重空管理, BUSINESS_DESC=现金及重空管理, PARENT_BUSINESS=200001, PARENT=false, BUSINESS_GRADE=2, BUSINESS_NO=7}, {BUSINESS_ID=WYLB00006, BUSINESS_NAME=资金异动管理, BUSINESS_DESC=资金异动管理, PARENT_BUSINESS=200001, PARENT=false, BUSINESS_GRADE=2, BUSINESS_NO=8}, {BUSINESS_ID=WYLB00007, BUSINESS_NAME=个人业务管理, BUSINESS_DESC=个人业务管理, PARENT_BUSINESS=200001, PARENT=false, BUSINESS_GRADE=2, BUSINESS_NO=9}, {BUSINESS_ID=WYLB00008, BUSINESS_NAME=其他, BUSINESS_DESC=其他, PARENT_BUSINESS=200001, PARENT=false, BUSINESS_GRADE=2, BUSINESS_NO=10}, {BUSINESS_ID=WYLB00009, BUSINESS_NAME=机构及柜员管理, BUSINESS_DESC=机构及柜员管理, PARENT_BUSINESS=200001, PARENT=false, BUSINESS_GRADE=2, BUSINESS_NO=11}]
2025-07-24 15:43:39.655 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/61257a6c932355d9] [http-nio-9058-exec-58] INFO  c.s.a.m.b.s.i.ModelBusinessLineTreeServiceImpl - 加载加业务条线树，结束
2025-07-24 15:43:39.669 [OrganNo_00023_UserNo_admin] [65b29fa111f1e324/526afed198fcb024] [http-nio-9058-exec-58] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"list":[
			{
				"BUSINESS_ID":"WYLB00001",
				"BUSINESS_NAME":"结算账户管理",
				"BUSINESS_DESC":"结算账户管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":3
			},
			{
				"BUSINESS_ID":"WYLB00002",
				"BUSINESS_NAME":"内部账户及科目管理",
				"BUSINESS_DESC":"内部账户及科目管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":4
			},
			{
				"BUSINESS_ID":"WYLB00003",
				"BUSINESS_NAME":"支付结算业务管理",
				"BUSINESS_DESC":"支付结算业务管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":5
			},
			{
				"BUSINESS_ID":"WYLB00004",
				"BUSINESS_NAME":"信贷业务管理",
				"BUSINESS_DESC":"信贷业务管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":6
			},
			{
				"BUSINESS_ID":"WYLB00005",
				"BUSINESS_NAME":"现金及重空管理",
				"BUSINESS_DESC":"现金及重空管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":7
			},
			{
				"BUSINESS_ID":"WYLB00006",
				"BUSINESS_NAME":"资金异动管理",
				"BUSINESS_DESC":"资金异动管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":8
			},
			{
				"BUSINESS_ID":"WYLB00007",
				"BUSINESS_NAME":"个人业务管理",
				"BUSINESS_DESC":"个人业务管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":9
			},
			{
				"BUSINESS_ID":"WYLB00008",
				"BUSINESS_NAME":"其他",
				"BUSINESS_DESC":"其他",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":10
			},
			{
				"BUSINESS_ID":"WYLB00009",
				"BUSINESS_NAME":"机构及柜员管理",
				"BUSINESS_DESC":"机构及柜员管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":11
			}
		]
	},
	"retMsg":"树初始化成功"
}
2025-07-24 15:43:39.948 [OrganNo_00023_UserNo_admin] [32341ea4f6d50647/b0af822fa5b12023] [http-nio-9058-exec-59] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"businessLine":"",
			"modelType":"0",
			"riskLevel":""
		}
	],
	"sysMap":{
		"oper_type":"getUserModelInfos"
	}
}
2025-07-24 15:43:39.965 [OrganNo_00023_UserNo_admin] [32341ea4f6d50647/5c4339a36fcf46f4] [http-nio-9058-exec-59] DEBUG c.s.a.s.b.d.R.selectFilterModelList - ==>  Preparing: select distinct model_id from SM_ROLE_MODEL_TB where role_no in ( ? )
2025-07-24 15:43:39.966 [OrganNo_00023_UserNo_admin] [32341ea4f6d50647/5c4339a36fcf46f4] [http-nio-9058-exec-59] DEBUG c.s.a.s.b.d.R.selectFilterModelList - ==> Parameters: 8(String)
2025-07-24 15:43:39.969 [OrganNo_00023_UserNo_admin] [32341ea4f6d50647/5c4339a36fcf46f4] [http-nio-9058-exec-59] DEBUG c.s.a.s.b.d.R.selectFilterModelList - <==      Total: 0
2025-07-24 15:43:39.969 [OrganNo_00023_UserNo_admin] [32341ea4f6d50647/5c4339a36fcf46f4] [http-nio-9058-exec-59] DEBUG c.s.a.m.h.d.M.getUserModelInfos - ==>  Preparing: select model_id AS "MODEL_ID", model_name AS "MODEL_NAME", unique_code AS "UNIQUE_CODE", create_mode AS "CREATE_MODE", model_status AS "MODEL_STATUS", model_code AS "MODEL_CODE", business_line AS "BUSINESS_LINE", run_mode AS "RUN_MODE", risk_level AS "RISK_LEVEL", model_desc AS "MODEL_DESC", retention_days AS "RETENTION_DAYS", create_orgno AS "CREATE_ORGNO", create_userno AS "CREATE_USERNO", create_username AS "CREATE_USERNAME", create_date AS "CREATE_DATE", is_open AS "IS_OPEN", is_lock AS "IS_LOCK", last_modi_date AS "LAST_MODI_DATE", is_push_clue AS "IS_PUSH_CLUE", clue_result AS "CLUE_RESULT", clue_org_field AS "CLUE_ORG_FIELD", feedback_days AS "FEEDBACK_DAYS", model_type AS "MODEL_TYPE", is_auto_yj AS "IS_AUTO_YJ", is_auto_supervise AS "IS_AUTO_SUPERVISE", is_cur_model AS "IS_CUR_MODEL" from MD_MODEL_INFO_TB WHERE MODEL_ID IN (SELECT MODEL_ID FROM md_model_org_tb WHERE ORGAN_NO = ?) and IS_PUSH_CLUE='1' and RUN_MODE = '2' AND MODEL_STATUS in ('2','3','5') AND MODEL_TYPE = ? order by MODEL_TYPE,BUSINESS_LINE,MODEL_ID
2025-07-24 15:43:39.970 [OrganNo_00023_UserNo_admin] [32341ea4f6d50647/5c4339a36fcf46f4] [http-nio-9058-exec-59] DEBUG c.s.a.m.h.d.M.getUserModelInfos - ==> Parameters: 00023(String), 0(String)
2025-07-24 15:43:39.974 [OrganNo_00023_UserNo_admin] [32341ea4f6d50647/5c4339a36fcf46f4] [http-nio-9058-exec-59] DEBUG c.s.a.m.h.d.M.getUserModelInfos - <==      Total: 0
2025-07-24 15:43:39.992 [OrganNo_00023_UserNo_admin] [32341ea4f6d50647/b0af822fa5b12023] [http-nio-9058-exec-59] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"userModelInfos":[
			
		]
	},
	"retMsg":"查询成功"
}
