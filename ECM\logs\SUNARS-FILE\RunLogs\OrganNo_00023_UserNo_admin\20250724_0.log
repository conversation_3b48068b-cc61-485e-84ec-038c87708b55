2025-07-24 16:13:06.251 [OrganNo_00023_UserNo_admin] [66ed12056f559406/dfcd18b2dddf4555] [http-nio-9110-exec-65] DEBUG c.s.cop.IF.controller.BaseController - 前台发送数据(脱敏前-生产INFO级别不输出):  "{\"parameterList\":[{}],\"sysMap\":{\"batchId\":\"********150114621932\",\"inputDate\":\"********\",\"fileName\":\"00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg\",\"siteNo\":\"\",\"contentId\":\"\",\"module\":\"ars-img\"}}"
2025-07-24 16:13:06.312 [OrganNo_00023_UserNo_admin] [66ed12056f559406/dfcd18b2dddf4555] [http-nio-9110-exec-65] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"batchId":"********150114621932",
		"inputDate":"********",
		"fileName":"00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg",
		"siteNo":"",
		"contentId":"",
		"module":"ars-img"
	}
}
2025-07-24 16:13:06.314 [OrganNo_00023_UserNo_admin] [66ed12056f559406/dfcd18b2dddf4555] [http-nio-9110-exec-65] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-24 16:13:06.314 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - [showImg]：batchId:********150114621932,contentId:,fileName:00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg
2025-07-24 16:13:06.316 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] DEBUG c.s.a.c.d.T.selectDataNps - ==>  Preparing: SELECT SERIAL_NO, BATCH_ID, INCCODEIN_BATCH, COPY_INCCODEIN, COPY_REC, IS_FRONT_PAGE, PS_LEVEL, PRIMARY_INCCODEIN, FORM_NAME, FORM_GROUP, VOUH_TYPE, CHECK_FLAG, ERROR_FLAG, IS_AUDIT, SELF_DELETE, MEMO, PROCESS_STATE, REC_DATE, REC_FAIL_CAUSE, FLOW_ID, IMAGE_SIZE, BACK_IMAGE_SIZE, PATCH_FLAG, FILE_NAME, BACK_FILE_NAME, OCR_WORKER, OCR_DATE, OCR_TIME, COPY_SERIALNO, IS_SIGN, SUPERVISE_DEAL, CONTENT_ID, DATA_SOURCE_ID, VOUCHER_NUMBER, FORM_ID,INSERT_DATE FROM BP_TMPDATA_1_TB WHERE BATCH_ID = ? AND (FILE_NAME = ? or BACK_FILE_NAME = ?) ORDER BY INCCODEIN_BATCH
2025-07-24 16:13:06.317 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] DEBUG c.s.a.c.d.T.selectDataNps - ==> Parameters: ********150114621932(String), 00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg(String), 00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg(String)
2025-07-24 16:13:06.319 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] DEBUG c.s.a.c.d.T.selectDataNps - <==      Total: 1
2025-07-24 16:13:06.336 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==>  Preparing: select BATCH_ID, BATCH_LOCK, BATCH_TOTAL_PAGE, BUSINESS_ID, BATCH_COMMIT, INPUT_DATE, TEMP_DATA_TABLE, INPUT_WORKER, MACHINE_ID, NEED_PROCESS, OCCUR_DATE, OPERATOR_NO, PROGRESS_FLAG, SITE_NO, INPUT_TIME, IMAGE_STATUS, IS_INVALID, PRIORITY_LEVEL, WORKER, WORK_TIME,PROCINSTID,OCR_FACTOR_FLAG,AREA_CODE,CONTENT_ID, REVIEW_REMARK, REVIEW_BY, REVIEW_TIME from BP_TMPBATCH_TB where BATCH_ID = ?
2025-07-24 16:13:06.337 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==> Parameters: ********150114621932(String)
2025-07-24 16:13:06.343 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] DEBUG c.s.a.c.d.T.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:13:06.359 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询
2025-07-24 16:13:06.359 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 16:13:06.359 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 16:13:06.365 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时发送的消息：0002<<::>>OPTION=HEIGHT_QUERY,XML=<HeightQuery IS_UNITED_ACCESS="false" USER_NAME="admin" COUNT="0" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt></HeightQuery>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 16:13:06.445 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>
2025-07-24 16:13:06.445 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 16:13:06.446 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询(over)
2025-07-24 16:13:06.446 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] INFO  com.sunyard.ars.common.util.ECMUtil - #######调用高级搜索返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>]#######
2025-07-24 16:13:06.460 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询
2025-07-24 16:13:06.461 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 16:13:06.461 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 16:13:06.463 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时发送的消息：0002<<::>>OPTION=QUERY,XML=<BatchBean IS_UNITED_ACCESS="false" MODEL_CODE="XCMS" USER="admin" IS_BREAK_POINT="false" IS_OWN_MD5="false" IS_DOWNLOAD="false" PASSWORD="MzMz" PASSWD="MzMz">  <index_Object CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" VERSION="0">    <customMap>      <CREATEDATE><string>********</string></CREATEDATE>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <FILTERS>        <entry>          <string>TRUENAME</string>          <string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string>        </entry>      </FILTERS>    </BatchFileBean>  </document_Objects></BatchBean>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 16:13:06.536 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="628857F1-9CB8-4369-616B-4A72BB661F4D" SAVE_NAME="628857F1-9CB8-4369-616B-4A72BB661F4D" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/628857F1-9CB8-4369-616B-4A72BB661F4D" OPTION_TYPE="1" FILE_SIZE="1016952" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJW4Z0oAABB5dcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string></TRUENAME>            <FILEMD5><string>72E18E33C052D8DFEC440ACE591842C3                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00003_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>
2025-07-24 16:13:06.537 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 16:13:06.537 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询(over)
2025-07-24 16:13:06.537 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] INFO  com.sunyard.ars.common.util.ECMUtil - #######查询批次返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="628857F1-9CB8-4369-616B-4A72BB661F4D" SAVE_NAME="628857F1-9CB8-4369-616B-4A72BB661F4D" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/628857F1-9CB8-4369-616B-4A72BB661F4D" OPTION_TYPE="1" FILE_SIZE="1016952" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJW4Z0oAABB5dcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string></TRUENAME>            <FILEMD5><string>72E18E33C052D8DFEC440ACE591842C3                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00003_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>]#######
2025-07-24 16:13:06.540 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - 成功获取到url
2025-07-24 16:13:06.562 [OrganNo_00023_UserNo_admin] [66ed12056f559406/dfcd18b2dddf4555] [http-nio-9110-exec-65] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"url":"http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJW4Z0oAABB5dcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU="
	},
	"retMsg":"查询影像信息成功"
}
2025-07-24 16:13:06.562 [OrganNo_00023_UserNo_admin] [66ed12056f559406/dfcd18b2dddf4555] [http-nio-9110-exec-65] DEBUG c.s.cop.IF.controller.BaseController - 请求编号：20250724161306225019，清空日志输出文件标识：OrganNo_00023_UserNo_admin
