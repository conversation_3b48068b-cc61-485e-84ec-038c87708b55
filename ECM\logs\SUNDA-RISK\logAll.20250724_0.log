2025-07-24 00:01:31.447 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 00:06:31.457 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 00:11:31.469 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 08:21:40.582 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 08:26:40.584 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 08:31:40.594 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 08:36:40.597 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 08:41:40.604 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 08:46:40.616 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 08:51:40.618 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 08:56:40.625 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 09:01:40.634 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 09:06:40.634 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 09:11:40.649 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 09:16:40.653 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 09:21:40.666 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 09:26:40.667 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 09:31:40.668 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 09:36:40.680 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 09:41:40.689 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 09:46:40.694 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 09:51:40.697 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 09:56:40.711 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 10:01:40.719 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 10:06:40.734 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 10:11:40.745 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 10:16:40.759 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 10:21:40.764 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 10:26:40.776 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 10:31:40.789 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 10:36:40.796 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 10:41:40.810 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 10:46:40.820 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 10:51:40.824 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 10:56:40.825 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 11:01:40.826 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 11:06:40.827 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 11:11:40.829 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 11:16:40.830 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 11:21:40.831 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 11:26:40.833 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 11:31:40.834 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 11:36:40.845 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 11:41:40.854 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 11:46:40.856 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 11:51:40.857 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 11:56:40.863 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 12:01:40.874 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 12:06:40.885 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 12:11:40.897 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 12:16:40.906 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 12:21:40.919 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 12:26:40.931 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 12:31:40.935 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 12:36:40.947 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 12:41:40.954 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 12:46:40.955 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 12:51:40.969 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 12:56:40.974 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 13:01:40.985 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 13:06:40.986 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 13:11:40.988 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 13:16:40.998 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 13:21:41.003 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 13:26:41.015 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 13:31:41.024 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 13:36:41.025 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 13:41:41.027 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 13:46:41.031 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 13:51:41.034 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 13:56:41.044 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 14:01:41.050 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 14:06:41.063 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 14:11:41.063 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 14:16:41.070 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 14:21:41.071 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 14:26:41.074 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 14:31:41.087 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 14:36:41.096 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 14:41:41.110 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 14:46:41.124 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 14:51:41.130 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 14:56:41.133 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 15:01:41.141 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 15:06:41.154 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 15:11:41.166 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 15:16:41.175 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 15:24:30.859 [] [/] [main] INFO  com.sunyard.RiskApplication - The following 1 profile is active: "dev"
2025-07-24 15:24:35.729 [] [/] [main] INFO  c.s.a.c.d.c.DynamicDataSourceLoading - 【加密类型】：AES
2025-07-24 15:24:35.749 [] [/] [main] INFO  c.s.a.c.d.c.DynamicDataSourceLoading - 【加密类型】：AES
2025-07-24 15:24:36.102 [] [/] [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2025-07-24 15:24:36.190 [] [/] [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2,db1} inited
2025-07-24 15:24:36.190 [] [/] [main] INFO  c.b.d.d.p.AbstractJdbcDataSourceProvider - 成功加载数据库驱动程序
2025-07-24 15:24:36.205 [] [/] [main] INFO  c.b.d.d.p.AbstractJdbcDataSourceProvider - 成功获取数据库连接
2025-07-24 15:24:36.286 [] [/] [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-3,master} inited
2025-07-24 15:24:36.369 [] [/] [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-4,db1} inited
2025-07-24 15:24:36.372 [] [/] [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [db1] success
2025-07-24 15:24:36.373 [] [/] [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-24 15:24:36.373 [] [/] [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-24 15:24:40.289 [] [/] [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2025-07-24 15:24:40.299 [] [/] [main] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-24 15:24:40.300 [] [/] [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
2025-07-24 15:24:40.301 [] [/] [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-07-24 15:24:40.302 [] [/] [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-24 15:24:40.302 [] [/] [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-24 15:24:40.302 [] [/] [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
2025-07-24 15:24:40.302 [] [/] [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@288e50c1
2025-07-24 15:24:40.541 [] [/] [main] INFO  c.s.a.c.filter.CSRFValidationFilter - 未配置CSRF验证请求源url，不进行Referer请求来源地址验证。
2025-07-24 15:24:40.567 [] [/] [main] INFO  c.sunyard.ars.common.ArsCommonConfig - 开始初始化风险公共内容。
2025-07-24 15:24:40.568 [] [/] [main] INFO  c.sunyard.ars.common.ArsCommonConfig - 数据库驱动名称：org.postgresql.Driver
2025-07-24 15:24:40.570 [] [/] [main] INFO  c.sunyard.ars.common.ArsCommonConfig - 初始化数据库类型VariableArs.dbType = DbTypeEnum{driverClassName='org.postgresql.Driver,', upperCase=false}
2025-07-24 15:24:40.570 [] [/] [main] INFO  c.sunyard.ars.common.ArsCommonConfig - 为DataConnectionUtil设置dataSource
2025-07-24 15:24:40.570 [] [/] [main] INFO  c.sunyard.ars.common.ArsCommonConfig - 初始化风险公共内容完成。
2025-07-24 15:24:41.308 [] [/] [main] INFO  c.s.a.c.d.config.DruidAuthConfig - 是否开启Druid授权访问：false
2025-07-24 15:24:41.767 [] [/] [main] INFO  com.sunyard.ars.risk.init.RiskInit - 开始初始化风险预警所需的差错流程节点信息。
2025-07-24 15:24:41.786 [] [/] [main] DEBUG c.s.a.r.d.a.O.etGetFormTypeFlowIds - ==>  Preparing: select item, name, val from ET_FORMTYPE_TB
2025-07-24 15:24:41.801 [] [/] [main] DEBUG c.s.a.r.d.a.O.etGetFormTypeFlowIds - ==> Parameters: 
2025-07-24 15:24:41.818 [] [/] [main] DEBUG c.s.a.r.d.a.O.etGetFormTypeFlowIds - <==      Total: 6
2025-07-24 15:24:41.820 [] [/] [main] INFO  com.sunyard.ars.risk.init.RiskInit - 初始化流程[{VAL=20210125090909793009, ITEM=3, NAME=预警单}, {VAL=20250415101822246005, ITEM=1, NAME=凭证核实单}, {VAL=20210303170400267005, ITEM=2, NAME=处理单}, {VAL=20250417170400267005, ITEM=7, NAME=金库流程单}, {VAL=20250418170400267005, ITEM=8, NAME=网点处理单}, {VAL=20210119101822246005, ITEM=0, NAME=整改单}]的节点信息。
2025-07-24 15:24:44.561 [] [/] [main] INFO  c.netflix.discovery.DiscoveryClient - Initializing Eureka in region us-east-1
2025-07-24 15:24:44.602 [] [/] [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using JSON encoding codec LegacyJacksonJson
2025-07-24 15:24:44.602 [] [/] [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using JSON decoding codec LegacyJacksonJson
2025-07-24 15:24:44.704 [] [/] [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using XML encoding codec XStreamXml
2025-07-24 15:24:44.705 [] [/] [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using XML decoding codec XStreamXml
2025-07-24 15:24:45.021 [] [/] [main] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 15:24:45.037 [] [/] [main] INFO  c.netflix.discovery.DiscoveryClient - Disable delta property : false
2025-07-24 15:24:45.038 [] [/] [main] INFO  c.netflix.discovery.DiscoveryClient - Single vip registry refresh property : null
2025-07-24 15:24:45.038 [] [/] [main] INFO  c.netflix.discovery.DiscoveryClient - Force full registry fetch : false
2025-07-24 15:24:45.038 [] [/] [main] INFO  c.netflix.discovery.DiscoveryClient - Application is null : false
2025-07-24 15:24:45.038 [] [/] [main] INFO  c.netflix.discovery.DiscoveryClient - Registered Applications size is zero : true
2025-07-24 15:24:45.038 [] [/] [main] INFO  c.netflix.discovery.DiscoveryClient - Application version is -1: true
2025-07-24 15:24:45.039 [] [/] [main] INFO  c.netflix.discovery.DiscoveryClient - Getting all instance registry info from the eureka server
2025-07-24 15:24:45.087 [] [/] [main] INFO  c.netflix.discovery.DiscoveryClient - The response status is 200
2025-07-24 15:24:45.090 [] [/] [main] INFO  c.netflix.discovery.DiscoveryClient - Starting heartbeat executor: renew interval is: 30
2025-07-24 15:24:45.093 [] [/] [main] INFO  c.n.discovery.InstanceInfoReplicator - InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-24 15:24:45.097 [] [/] [main] INFO  c.netflix.discovery.DiscoveryClient - Discovery Client initialized at timestamp 1753341885096 with initial instances count: 9
2025-07-24 15:24:45.172 [] [/] [main] INFO  com.sunyard.ars.risk.init.RiskInit - 初始化风险预警所需的差错流程节点信息：{0={56=重新回复整改, 93=已撤销, 61=待复核整改, 94=确认已整改, 51=待网点整改}, 1={56=网点重新回复, 93=已核销, 94=已确认, 61=待确认, 21=待核实}, 2={56=待网点重新回复, 58=待网点重新回复, 15=中心退回, 93=已撤销, 61=待复核核实, 94=已确认, 51=网点回复, 52=下发需要整改, 21=待审核, 65=分管主任审核}, 3={12=中心退回, 24=待网点确认, 29=回复待办结, 93=核销, 95=转差错办结}, 7={56=待网点重新回复, 58=待网点重新回复, 15=中心退回, 93=已撤销, 94=已确认, 61=待复核核实, 51=网点回复, 52=需整改, 65=分管主任审核, 21=待审核}, 8={56=待网点重新回复, 58=待网点重新回复, 15=中心退回, 93=已撤销, 61=待复核核实, 94=已确认, 51=网点回复, 52=需整改, 65=分管主任审核, 21=待审核}}
2025-07-24 15:24:45.573 [] [/] [main] INFO  org.redisson.Version - Redisson 3.26.1
2025-07-24 15:24:45.848 [] [/] [redisson-netty-4-4] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-24 15:24:45.865 [] [/] [redisson-netty-4-13] INFO  o.r.connection.ConnectionsHolder - 5 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-24 15:24:46.272 [] [/] [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-07-24 15:24:48.205 [] [/] [main] INFO  c.netflix.discovery.DiscoveryClient - Saw local status change event StatusChangeEvent [timestamp=1753341888205, current=UP, previous=STARTING]
2025-07-24 15:24:48.208 [] [/] [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_SUNDA-RISK/localhost:9060: registering service...
2025-07-24 15:24:48.234 [] [/] [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_SUNDA-RISK/localhost:9060 - registration status: 204
2025-07-24 15:24:50.553 [] [/] [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-24 15:24:50.563 [] [/] [main] INFO  c.s.aos.common.config.AosConfigs - ------------- 注入配置  ---------------
2025-07-24 15:24:50.564 [] [/] [main] INFO  c.s.aos.common.config.AosConfigs - ------------- 获取配置文件参数开始  ---------------
2025-07-24 15:24:50.565 [] [/] [main] INFO  c.s.aos.common.config.AosConfigs - ------------- 获取配置文件参数完成！ ---------------
2025-07-24 15:24:50.572 [] [/] [main] INFO  c.s.a.c.util.easyExcel.ExportUtil - 获取配置的excel下载文件夹路径/home/<USER>/template/downloadPath/
2025-07-24 15:24:50.572 [] [/] [main] INFO  c.s.a.c.util.easyExcel.ExportUtil - 填充模板文件目录/home/<USER>/template/fillTemplate/
2025-07-24 15:24:50.575 [] [/] [main] INFO  c.s.a.common.util.easyExcel.FillUtil - 填充模板文件目录/home/<USER>/template/fillTemplate/
2025-07-24 15:24:50.575 [] [/] [main] INFO  c.s.a.common.util.easyExcel.FillUtil - 导出文件目录/home/<USER>/template/downloadPath/
2025-07-24 15:24:50.604 [] [/] [main] INFO  com.sunyard.RiskApplication - Started RiskApplication in 27.167 seconds (JVM running for 29.566)
2025-07-24 15:24:50.617 [] [/] [main] INFO  c.s.aos.common.init.InitCurrServer - 初始化服务「SUNDA-RISK」 |获取应用服务信息配置 | 开始执行
2025-07-24 15:24:51.497 [] [/] [main] INFO  c.s.aos.common.init.InitCurrServer - 「应用服务id」20190129000000000001 | 「地址」127.0.0.1:9007 | 「访问目录」/ | 「服务类型」0 | 「启用状态」1
2025-07-24 15:24:51.497 [] [/] [main] INFO  c.s.aos.common.init.InitCurrServer - 「应用服务id」20191217154129494011 | 「地址」**************:8089 | 「访问目录」/SunAOS | 「服务类型」2 | 「启用状态」1
2025-07-24 15:24:51.500 [] [/] [main] ERROR c.s.aos.common.init.InitCurrServer - 初始化服务「SUNDA-RISK」 |执行失败 初始化当前应用服务信息异常，未获取到匹配的应用服务配置信息，请检查「应用服务配置」中是否包含当前服务！
com.sunyard.aos.common.exception.SunAOSException: 初始化当前应用服务信息异常，未获取到匹配的应用服务配置信息，请检查「应用服务配置」中是否包含当前服务！
2025-07-24 15:24:51.501 [] [/] [main] INFO  c.s.aos.common.init.InitCurrServer - 初始化服务「SUNDA-RISK」 |获取应用服务信息配置 | 开始执行
2025-07-24 15:24:52.343 [] [/] [main] INFO  c.s.aos.common.init.InitCurrServer - 「应用服务id」20190129000000000001 | 「地址」127.0.0.1:9007 | 「访问目录」/ | 「服务类型」0 | 「启用状态」1
2025-07-24 15:24:52.345 [] [/] [main] INFO  c.s.aos.common.init.InitCurrServer - 「应用服务id」20191217154129494011 | 「地址」**************:8089 | 「访问目录」/SunAOS | 「服务类型」2 | 「启用状态」1
2025-07-24 15:24:52.345 [] [/] [main] ERROR c.s.aos.common.init.InitCurrServer - 初始化服务「SUNDA-RISK」 |执行失败 初始化当前应用服务信息异常，未获取到匹配的应用服务配置信息，请检查「应用服务配置」中是否包含当前服务！
com.sunyard.aos.common.exception.SunAOSException: 初始化当前应用服务信息异常，未获取到匹配的应用服务配置信息，请检查「应用服务配置」中是否包含当前服务！
2025-07-24 15:24:54.399 [] [/] [registrationTask1] WARN  d.c.b.a.c.r.ApplicationRegistrator - Failed to register application as Application(name=SUNDA-RISK, managementUrl=http://************:9060/actuator, healthUrl=http://************:9060/actuator/health, serviceUrl=http://************:9060/) at spring-boot-admin ([http://127.0.0.1:8878/admin/instances]): I/O error on POST request for "http://127.0.0.1:8878/admin/instances": Connect to 127.0.0.1:8878 [/127.0.0.1] failed: Connection refused: connect; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8878 [/127.0.0.1] failed: Connection refused: connect. Further attempts are logged on DEBUG level
2025-07-24 15:29:45.048 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 15:34:45.056 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 15:39:45.069 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 15:43:41.204 [] [feff2c01d62255cc/3a1bd4a966234a95] [http-nio-9060-exec-1] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警模型列表!请求IP地址: ************** 操作开始时间: 2025-07-24 15:43:41 操作结束时间: 2025-07-24 15:43:41!总共花费时间: 184 毫秒！
2025-07-24 15:43:42.387 [] [906c02123d3a64bc/f4bf893bf74bf70c] [http-nio-9060-exec-2] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 查询预警信息统计数据!请求IP地址: ************** 操作开始时间: 2025-07-24 15:43:42 操作结束时间: 2025-07-24 15:43:42!总共花费时间: 164 毫秒！
2025-07-24 15:43:49.470 [OrganNo_00023_UserNo_admin] [bc0d9697a5c35988/4663a62ce5484b29] [http-nio-9060-exec-4] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"end_date":"********",
			"model_id":"",
			"site_no":"",
			"start_date":"20240128"
		}
	],
	"sysMap":{
		
	}
}
2025-07-24 15:43:49.492 [OrganNo_00023_UserNo_admin] [bc0d9697a5c35988/79a4e62806bb54f0] [http-nio-9060-exec-4] DEBUG c.s.a.r.d.a.A.getRiskWarningStatistics - ==>  Preparing: SELECT COALESCE(main.NOT_DEALED_COUNT, 0) as NOT_DEALED_COUNT, COALESCE(main.HAVE_DEALED_COUNT, 0) as HAVE_DEALED_COUNT, COALESCE(main.SUPERVISE_PASS_COUNT, 0) as SUPERVISE_PASS_COUNT, COALESCE(main.SUPERVISE_SLIP_COUNT, 0) as SUPERVISE_SLIP_COUNT, COALESCE(back.BACKCOUNT, 0) as BACKCOUNT, main.MODEL_ID FROM ( SELECT sum(a.NOT_DEALED_COUNT) as NOT_DEALED_COUNT, sum(a.HAVE_DEALED_COUNT) as HAVE_DEALED_COUNT, sum(a.SUPERVISE_PASS_COUNT) as SUPERVISE_PASS_COUNT, sum(a.SUPERVISE_SLIP_COUNT) as SUPERVISE_SLIP_COUNT, a.MODEL_ID FROM SUPERVISE_STATISTIC_TB a, SM_USER_ORGAN_TB b WHERE 1=1 AND a.OCCUR_DATE >= ? AND a.OCCUR_DATE <= ? AND a.site_no = b.organ_no AND b.user_no = ? AND a.user_no = b.user_no GROUP BY a.MODEL_ID ) main LEFT JOIN ( SELECT COUNT(*) as BACKCOUNT, CAST(model_id AS NUMERIC) as MODEL_ID FROM mc_model_zjd WHERE state = '3' AND user_no_s = ? AND business_date >= ? AND business_date <= ? GROUP BY model_id ) back ON main.MODEL_ID = back.MODEL_ID ORDER BY main.MODEL_ID
2025-07-24 15:43:49.492 [OrganNo_00023_UserNo_admin] [bc0d9697a5c35988/79a4e62806bb54f0] [http-nio-9060-exec-4] DEBUG c.s.a.r.d.a.A.getRiskWarningStatistics - ==> Parameters: 20240128(String), ********(String), admin(String), admin(String), 20240128(String), ********(String)
2025-07-24 15:43:49.512 [OrganNo_00023_UserNo_admin] [bc0d9697a5c35988/79a4e62806bb54f0] [http-nio-9060-exec-4] DEBUG c.s.a.r.d.a.A.getRiskWarningStatistics - <==      Total: 1
2025-07-24 15:43:49.542 [OrganNo_00023_UserNo_admin] [bc0d9697a5c35988/4663a62ce5484b29] [http-nio-9060-exec-4] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"riskWarningStats":[
			{
				"MODEL_ID":6,
				"SUPERVISE_SLIP_COUNT":0,
				"HAVE_DEALED_COUNT":0,
				"NOT_DEALED_COUNT":2,
				"BACKCOUNT":0,
				"SUPERVISE_PASS_COUNT":0
			}
		]
	},
	"retMsg":"查询成功"
}
2025-07-24 15:43:49.544 [] [bc0d9697a5c35988/4663a62ce5484b29] [http-nio-9060-exec-4] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 查询预警信息统计数据!请求IP地址: ************** 操作开始时间: 2025-07-24 15:43:49 操作结束时间: 2025-07-24 15:43:49!总共花费时间: 81 毫秒！
2025-07-24 15:44:01.002 [OrganNo_00023_UserNo_admin] [18a23acbf88262d7/b424a8f09d33c67c] [http-nio-9060-exec-6] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"end_date":"********",
			"model_id":"",
			"site_no":"",
			"start_date":"20240128"
		}
	],
	"sysMap":{
		
	}
}
2025-07-24 15:44:01.027 [OrganNo_00023_UserNo_admin] [18a23acbf88262d7/8a453ab5b4457446] [http-nio-9060-exec-6] DEBUG c.s.a.r.d.a.A.getRiskWarningStatistics - ==>  Preparing: SELECT COALESCE(main.NOT_DEALED_COUNT, 0) as NOT_DEALED_COUNT, COALESCE(main.HAVE_DEALED_COUNT, 0) as HAVE_DEALED_COUNT, COALESCE(main.SUPERVISE_PASS_COUNT, 0) as SUPERVISE_PASS_COUNT, COALESCE(main.SUPERVISE_SLIP_COUNT, 0) as SUPERVISE_SLIP_COUNT, COALESCE(back.BACKCOUNT, 0) as BACKCOUNT, main.MODEL_ID FROM ( SELECT sum(a.NOT_DEALED_COUNT) as NOT_DEALED_COUNT, sum(a.HAVE_DEALED_COUNT) as HAVE_DEALED_COUNT, sum(a.SUPERVISE_PASS_COUNT) as SUPERVISE_PASS_COUNT, sum(a.SUPERVISE_SLIP_COUNT) as SUPERVISE_SLIP_COUNT, a.MODEL_ID FROM SUPERVISE_STATISTIC_TB a, SM_USER_ORGAN_TB b WHERE 1=1 AND a.OCCUR_DATE >= ? AND a.OCCUR_DATE <= ? AND a.site_no = b.organ_no AND b.user_no = ? AND a.user_no = b.user_no GROUP BY a.MODEL_ID ) main LEFT JOIN ( SELECT COUNT(*) as BACKCOUNT, CAST(model_id AS NUMERIC) as MODEL_ID FROM mc_model_zjd WHERE state = '3' AND user_no_s = ? AND business_date >= ? AND business_date <= ? GROUP BY model_id ) back ON main.MODEL_ID = back.MODEL_ID ORDER BY main.MODEL_ID
2025-07-24 15:44:01.028 [OrganNo_00023_UserNo_admin] [18a23acbf88262d7/8a453ab5b4457446] [http-nio-9060-exec-6] DEBUG c.s.a.r.d.a.A.getRiskWarningStatistics - ==> Parameters: 20240128(String), ********(String), admin(String), admin(String), 20240128(String), ********(String)
2025-07-24 15:44:01.046 [OrganNo_00023_UserNo_admin] [18a23acbf88262d7/8a453ab5b4457446] [http-nio-9060-exec-6] DEBUG c.s.a.r.d.a.A.getRiskWarningStatistics - <==      Total: 1
2025-07-24 15:44:01.075 [OrganNo_00023_UserNo_admin] [18a23acbf88262d7/b424a8f09d33c67c] [http-nio-9060-exec-6] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"riskWarningStats":[
			{
				"MODEL_ID":6,
				"SUPERVISE_SLIP_COUNT":0,
				"HAVE_DEALED_COUNT":0,
				"NOT_DEALED_COUNT":2,
				"BACKCOUNT":0,
				"SUPERVISE_PASS_COUNT":0
			}
		]
	},
	"retMsg":"查询成功"
}
2025-07-24 15:44:01.076 [] [18a23acbf88262d7/b424a8f09d33c67c] [http-nio-9060-exec-6] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 查询预警信息统计数据!请求IP地址: ************** 操作开始时间: 2025-07-24 15:44:00 操作结束时间: 2025-07-24 15:44:01!总共花费时间: 81 毫秒！
2025-07-24 15:44:02.148 [] [2cf4307e724e55c5/6f32703225282672] [http-nio-9060-exec-7] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 查询预警信息统计数据!请求IP地址: ************** 操作开始时间: 2025-07-24 15:44:02 操作结束时间: 2025-07-24 15:44:02!总共花费时间: 96 毫秒！
2025-07-24 15:44:03.383 [] [c2105546217fa3ae/4c895370d368e854] [http-nio-9060-exec-8] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警明细数据!请求IP地址: ************** 操作开始时间: 2025-07-24 15:44:03 操作结束时间: 2025-07-24 15:44:03!总共花费时间: 71 毫秒！
2025-07-24 15:44:03.461 [] [56b1ddd2f5ea3aba/eb6a9723aa5a6ffe] [http-nio-9060-exec-9] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警明细数据!请求IP地址: ************** 操作开始时间: 2025-07-24 15:44:03 操作结束时间: 2025-07-24 15:44:03!总共花费时间: 66 毫秒！
2025-07-24 15:44:09.184 [] [3cf033417395433e/68a2825aa74e74a7] [http-nio-9060-exec-11] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警模型列表!请求IP地址: ************** 操作开始时间: 2025-07-24 15:44:09 操作结束时间: 2025-07-24 15:44:09!总共花费时间: 33 毫秒！
2025-07-24 15:44:09.308 [] [35de0d0d3dcf9373/04edd0f03fed7439] [http-nio-9060-exec-12] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 查询预警信息统计数据!请求IP地址: ************** 操作开始时间: 2025-07-24 15:44:09 操作结束时间: 2025-07-24 15:44:09!总共花费时间: 100 毫秒！
2025-07-24 15:44:45.074 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 15:49:45.081 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 15:54:45.085 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 15:58:50.276 [] [c06fa5d416faff14/d7db756a02161221] [http-nio-9060-exec-14] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 查询预警信息统计数据!请求IP地址: ************** 操作开始时间: 2025-07-24 15:58:50 操作结束时间: 2025-07-24 15:58:50!总共花费时间: 181 毫秒！
2025-07-24 15:59:45.095 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 16:00:24.629 [] [0c698ec13ef6a289/a536d1dff704a7aa] [http-nio-9060-exec-16] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警明细数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:00:24 操作结束时间: 2025-07-24 16:00:24!总共花费时间: 61 毫秒！
2025-07-24 16:00:24.706 [] [55fcb60d8d8675d7/8a5c45923248772f] [http-nio-9060-exec-17] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警明细数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:00:24 操作结束时间: 2025-07-24 16:00:24!总共花费时间: 65 毫秒！
2025-07-24 16:00:26.639 [] [1160d297173f0056/f8bb0ccdb758ba6f] [http-nio-9060-exec-18] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警模型列表!请求IP地址: ************** 操作开始时间: 2025-07-24 16:00:26 操作结束时间: 2025-07-24 16:00:26!总共花费时间: 52 毫秒！
2025-07-24 16:00:26.747 [] [6d23a8a7f217c62a/a4b4c0ecfd2ae209] [http-nio-9060-exec-19] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 查询预警信息统计数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:00:26 操作结束时间: 2025-07-24 16:00:26!总共花费时间: 92 毫秒！
2025-07-24 16:00:28.374 [] [b1da0c51f7b6a6ae/4bbc052c104928de] [http-nio-9060-exec-20] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警明细数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:00:28 操作结束时间: 2025-07-24 16:00:28!总共花费时间: 61 毫秒！
2025-07-24 16:00:28.467 [] [7e5c2cbdf5ae333a/52d22b00b18a95f3] [http-nio-9060-exec-21] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警明细数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:00:28 操作结束时间: 2025-07-24 16:00:28!总共花费时间: 79 毫秒！
2025-07-24 16:00:31.163 [] [c2424112ad171e66/4c9bda8e51f3ff1f] [http-nio-9060-exec-22] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警模型列表!请求IP地址: ************** 操作开始时间: 2025-07-24 16:00:31 操作结束时间: 2025-07-24 16:00:31!总共花费时间: 53 毫秒！
2025-07-24 16:00:31.268 [] [e300108bcb011d76/11ce2dc28e068b33] [http-nio-9060-exec-23] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 查询预警信息统计数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:00:31 操作结束时间: 2025-07-24 16:00:31!总共花费时间: 88 毫秒！
2025-07-24 16:00:34.939 [] [7bf478b075bf97fd/f0acc65325ea2caa] [http-nio-9060-exec-25] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警明细数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:00:34 操作结束时间: 2025-07-24 16:00:34!总共花费时间: 59 毫秒！
2025-07-24 16:00:35.018 [] [5ed98f9e39a6981f/a23610b6e6909b60] [http-nio-9060-exec-26] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警明细数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:00:34 操作结束时间: 2025-07-24 16:00:35!总共花费时间: 67 毫秒！
2025-07-24 16:00:38.404 [] [83ad0f6c3a581093/f103870b75576104] [http-nio-9060-exec-28] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警模型列表!请求IP地址: ************** 操作开始时间: 2025-07-24 16:00:38 操作结束时间: 2025-07-24 16:00:38!总共花费时间: 33 毫秒！
2025-07-24 16:00:38.514 [] [c729f474709f2500/1d3d8c628357f1c3] [http-nio-9060-exec-29] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 查询预警信息统计数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:00:38 操作结束时间: 2025-07-24 16:00:38!总共花费时间: 94 毫秒！
2025-07-24 16:01:52.751 [] [ad74e2da97e29c89/d9afebaa10c160e1] [http-nio-9060-exec-31] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 查询预警信息统计数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:01:52 操作结束时间: 2025-07-24 16:01:52!总共花费时间: 87 毫秒！
2025-07-24 16:01:54.233 [] [20662db2dae263c0/3b468d85c888c18f] [http-nio-9060-exec-32] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 查询预警信息统计数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:01:54 操作结束时间: 2025-07-24 16:01:54!总共花费时间: 101 毫秒！
2025-07-24 16:01:54.729 [] [9c710e4d4f0fc74d/d73682e32a92168d] [http-nio-9060-exec-33] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 查询预警信息统计数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:01:54 操作结束时间: 2025-07-24 16:01:54!总共花费时间: 95 毫秒！
2025-07-24 16:01:54.949 [] [a97ca53db9f22998/686b151b18fd2382] [http-nio-9060-exec-34] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 查询预警信息统计数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:01:54 操作结束时间: 2025-07-24 16:01:54!总共花费时间: 95 毫秒！
2025-07-24 16:01:55.193 [] [e9ea980a0ba9c58c/5c804f089bfd1221] [http-nio-9060-exec-35] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 查询预警信息统计数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:01:55 操作结束时间: 2025-07-24 16:01:55!总共花费时间: 100 毫秒！
2025-07-24 16:01:55.407 [] [cd4bb611cf53c1b2/a0d7c7f1a315a6c8] [http-nio-9060-exec-36] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 查询预警信息统计数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:01:55 操作结束时间: 2025-07-24 16:01:55!总共花费时间: 103 毫秒！
2025-07-24 16:01:59.562 [] [f6fe2de341a69184/2a89700ba6d426c8] [http-nio-9060-exec-38] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警明细数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:01:59 操作结束时间: 2025-07-24 16:01:59!总共花费时间: 51 毫秒！
2025-07-24 16:01:59.640 [] [72ee0c1307871517/af63f24ed7040d85] [http-nio-9060-exec-39] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警明细数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:01:59 操作结束时间: 2025-07-24 16:01:59!总共花费时间: 66 毫秒！
2025-07-24 16:02:00.589 [] [4cbd69c7f1f6120d/964019e2080f022c] [http-nio-9060-exec-40] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警模型列表!请求IP地址: ************** 操作开始时间: 2025-07-24 16:02:00 操作结束时间: 2025-07-24 16:02:00!总共花费时间: 54 毫秒！
2025-07-24 16:02:00.693 [] [10c380b9ed762d6a/9da787a489578c2b] [http-nio-9060-exec-41] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 查询预警信息统计数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:02:00 操作结束时间: 2025-07-24 16:02:00!总共花费时间: 90 毫秒！
2025-07-24 16:02:01.839 [] [1b42d68b51bae1b1/615fa8514331dff9] [http-nio-9060-exec-42] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 查询预警信息统计数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:02:01 操作结束时间: 2025-07-24 16:02:01!总共花费时间: 112 毫秒！
2025-07-24 16:02:02.206 [] [4189bad496265769/2484e05ef6214d9e] [http-nio-9060-exec-43] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 查询预警信息统计数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:02:02 操作结束时间: 2025-07-24 16:02:02!总共花费时间: 105 毫秒！
2025-07-24 16:02:02.436 [] [60dfc49e3aedd8d5/fb424c6793927d5b] [http-nio-9060-exec-44] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 查询预警信息统计数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:02:02 操作结束时间: 2025-07-24 16:02:02!总共花费时间: 98 毫秒！
2025-07-24 16:02:12.053 [] [f13dec77aeb91825/2aa01efa453f4c30] [http-nio-9060-exec-46] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警明细数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:02:11 操作结束时间: 2025-07-24 16:02:12!总共花费时间: 62 毫秒！
2025-07-24 16:02:12.145 [] [da086f2b6eee5203/249ea5ea4db1f298] [http-nio-9060-exec-47] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警明细数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:02:12 操作结束时间: 2025-07-24 16:02:12!总共花费时间: 78 毫秒！
2025-07-24 16:02:13.266 [] [2c962b43978c72b4/00a8bbb5a51beebe] [http-nio-9060-exec-48] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警模型列表!请求IP地址: ************** 操作开始时间: 2025-07-24 16:02:13 操作结束时间: 2025-07-24 16:02:13!总共花费时间: 56 毫秒！
2025-07-24 16:02:13.374 [] [268c615da72616f3/5ddee5e703b07195] [http-nio-9060-exec-49] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 查询预警信息统计数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:02:13 操作结束时间: 2025-07-24 16:02:13!总共花费时间: 92 毫秒！
2025-07-24 16:02:14.696 [] [892ba0990eb4792f/ce97b0cbeddc18b3] [http-nio-9060-exec-50] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警明细数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:02:14 操作结束时间: 2025-07-24 16:02:14!总共花费时间: 65 毫秒！
2025-07-24 16:02:14.773 [] [7d41c73acf16c195/157e9d650a820736] [http-nio-9060-exec-51] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警明细数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:02:14 操作结束时间: 2025-07-24 16:02:14!总共花费时间: 65 毫秒！
2025-07-24 16:02:15.627 [] [3efe64ed41906214/fbfc53be82310f51] [http-nio-9060-exec-52] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警模型列表!请求IP地址: ************** 操作开始时间: 2025-07-24 16:02:15 操作结束时间: 2025-07-24 16:02:15!总共花费时间: 47 毫秒！
2025-07-24 16:02:15.733 [] [d92e41f28ac900f5/1e2ae471047727c5] [http-nio-9060-exec-53] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 查询预警信息统计数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:02:15 操作结束时间: 2025-07-24 16:02:15!总共花费时间: 90 毫秒！
2025-07-24 16:02:17.422 [] [2a8a9eed07782d59/b553c1b6397ba740] [http-nio-9060-exec-54] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警明细数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:02:17 操作结束时间: 2025-07-24 16:02:17!总共花费时间: 58 毫秒！
2025-07-24 16:02:17.501 [] [c6068d6d2c603a8a/7e189a237112fa30] [http-nio-9060-exec-55] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警明细数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:02:17 操作结束时间: 2025-07-24 16:02:17!总共花费时间: 68 毫秒！
2025-07-24 16:02:19.230 [] [7285250d92b9cc46/59792e46dbfd4c81] [http-nio-9060-exec-56] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警模型列表!请求IP地址: ************** 操作开始时间: 2025-07-24 16:02:19 操作结束时间: 2025-07-24 16:02:19!总共花费时间: 57 毫秒！
2025-07-24 16:02:19.335 [] [585ea4f2d5e5adae/560635e9cf9b6703] [http-nio-9060-exec-57] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 查询预警信息统计数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:02:19 操作结束时间: 2025-07-24 16:02:19!总共花费时间: 88 毫秒！
2025-07-24 16:02:21.063 [] [1d638764f08da10a/dc0f63924f13e889] [http-nio-9060-exec-58] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警明细数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:02:20 操作结束时间: 2025-07-24 16:02:21!总共花费时间: 65 毫秒！
2025-07-24 16:02:21.140 [] [4f40a61a98987b78/658501c5efa2bb11] [http-nio-9060-exec-59] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警明细数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:02:21 操作结束时间: 2025-07-24 16:02:21!总共花费时间: 65 毫秒！
2025-07-24 16:02:22.398 [] [8cb26b9780e27125/4bed029d4b7a4036] [http-nio-9060-exec-60] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警模型列表!请求IP地址: ************** 操作开始时间: 2025-07-24 16:02:22 操作结束时间: 2025-07-24 16:02:22!总共花费时间: 51 毫秒！
2025-07-24 16:02:22.504 [] [76fe9a007eef2305/1ad4617e3093dfb0] [http-nio-9060-exec-61] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 查询预警信息统计数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:02:22 操作结束时间: 2025-07-24 16:02:22!总共花费时间: 91 毫秒！
2025-07-24 16:02:36.367 [] [b5354de6aef4d62c/d755c0ef9dcc92a2] [http-nio-9060-exec-63] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警明细数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:02:36 操作结束时间: 2025-07-24 16:02:36!总共花费时间: 57 毫秒！
2025-07-24 16:02:36.443 [] [a0e3f43b53dd5b86/5f1ee3169e4448fd] [http-nio-9060-exec-64] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警明细数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:02:36 操作结束时间: 2025-07-24 16:02:36!总共花费时间: 66 毫秒！
2025-07-24 16:02:37.627 [] [5748fc10c6bba4d9/1105e2eda4882e2c] [http-nio-9060-exec-65] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警模型列表!请求IP地址: ************** 操作开始时间: 2025-07-24 16:02:37 操作结束时间: 2025-07-24 16:02:37!总共花费时间: 51 毫秒！
2025-07-24 16:02:37.735 [] [8ac798a2acedef80/c9c536ca22bf88f6] [http-nio-9060-exec-66] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 查询预警信息统计数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:02:37 操作结束时间: 2025-07-24 16:02:37!总共花费时间: 92 毫秒！
2025-07-24 16:02:40.679 [] [660adefef11f5ae1/60b5d7981926bf30] [http-nio-9060-exec-67] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警明细数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:02:40 操作结束时间: 2025-07-24 16:02:40!总共花费时间: 47 毫秒！
2025-07-24 16:02:40.755 [] [6cb670133923b015/c7f1245ebb0d2df6] [http-nio-9060-exec-68] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警明细数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:02:40 操作结束时间: 2025-07-24 16:02:40!总共花费时间: 67 毫秒！
2025-07-24 16:02:44.765 [] [8e6b7874dbc6c454/ea7b963f476aa14c] [http-nio-9060-exec-70] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警模型列表!请求IP地址: ************** 操作开始时间: 2025-07-24 16:02:44 操作结束时间: 2025-07-24 16:02:44!总共花费时间: 46 毫秒！
2025-07-24 16:02:44.871 [] [831a0ec6513e2541/8a78ca7194a909c7] [http-nio-9060-exec-71] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 查询预警信息统计数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:02:44 操作结束时间: 2025-07-24 16:02:44!总共花费时间: 89 毫秒！
2025-07-24 16:02:46.315 [] [3b59851ccb16c287/4c9f91ae30688a3d] [http-nio-9060-exec-72] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警明细数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:02:46 操作结束时间: 2025-07-24 16:02:46!总共花费时间: 54 毫秒！
2025-07-24 16:02:46.389 [] [c0cc88f1d4029d4c/fc61dda86221437e] [http-nio-9060-exec-73] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警明细数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:02:46 操作结束时间: 2025-07-24 16:02:46!总共花费时间: 63 毫秒！
2025-07-24 16:02:48.413 [] [fba40985b00551e5/bcd268b149695542] [http-nio-9060-exec-74] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警模型列表!请求IP地址: ************** 操作开始时间: 2025-07-24 16:02:48 操作结束时间: 2025-07-24 16:02:48!总共花费时间: 47 毫秒！
2025-07-24 16:02:48.506 [] [61387c8c294b15e8/10332e444865f8c9] [http-nio-9060-exec-75] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 查询预警信息统计数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:02:48 操作结束时间: 2025-07-24 16:02:48!总共花费时间: 79 毫秒！
2025-07-24 16:03:55.327 [] [03b6a723f2fab7ac/53a93829ae2fb14b] [http-nio-9060-exec-76] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警明细数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:03:55 操作结束时间: 2025-07-24 16:03:55!总共花费时间: 55 毫秒！
2025-07-24 16:03:55.405 [] [ab26c9e1d797fb67/0c9db60d57e4a2f1] [http-nio-9060-exec-77] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警明细数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:03:55 操作结束时间: 2025-07-24 16:03:55!总共花费时间: 68 毫秒！
2025-07-24 16:04:31.950 [] [9eca3d10541bfb9e/fd08d5119fcf1fe2] [http-nio-9060-exec-79] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警模型列表!请求IP地址: ************** 操作开始时间: 2025-07-24 16:04:31 操作结束时间: 2025-07-24 16:04:31!总共花费时间: 47 毫秒！
2025-07-24 16:04:32.087 [] [371681afc76f892f/f6e91260cd340e3f] [http-nio-9060-exec-81] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 查询预警信息统计数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:04:31 操作结束时间: 2025-07-24 16:04:32!总共花费时间: 116 毫秒！
2025-07-24 16:04:33.669 [] [e975ad09e4363f54/afb4aa92d4066e86] [http-nio-9060-exec-82] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警明细数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:04:33 操作结束时间: 2025-07-24 16:04:33!总共花费时间: 85 毫秒！
2025-07-24 16:04:33.793 [] [aaf6112817fa08e4/2b1c681435c96009] [http-nio-9060-exec-83] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警明细数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:04:33 操作结束时间: 2025-07-24 16:04:33!总共花费时间: 101 毫秒！
2025-07-24 16:04:45.103 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 16:05:43.214 [] [7d5125a474388015/6bdb38c0560c0791] [http-nio-9060-exec-84] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警模型列表!请求IP地址: ************** 操作开始时间: 2025-07-24 16:05:43 操作结束时间: 2025-07-24 16:05:43!总共花费时间: 39 毫秒！
2025-07-24 16:05:43.320 [] [3ab0b52990c86622/412aa411a1e6b048] [http-nio-9060-exec-86] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 查询预警信息统计数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:05:43 操作结束时间: 2025-07-24 16:05:43!总共花费时间: 91 毫秒！
2025-07-24 16:05:44.671 [] [9033f32962df80da/771199ab0b1df467] [http-nio-9060-exec-87] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 查询预警信息统计数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:05:44 操作结束时间: 2025-07-24 16:05:44!总共花费时间: 86 毫秒！
2025-07-24 16:05:46.697 [] [d1e2fb95959d3d5b/8f473f6fef365842] [http-nio-9060-exec-88] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警明细数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:05:46 操作结束时间: 2025-07-24 16:05:46!总共花费时间: 56 毫秒！
2025-07-24 16:05:46.791 [] [30df206121285980/1d6f08031768dbe4] [http-nio-9060-exec-89] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警明细数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:05:46 操作结束时间: 2025-07-24 16:05:46!总共花费时间: 82 毫秒！
2025-07-24 16:05:47.497 [] [717beca1f620de59/8459a6b6e4c8ddff] [http-nio-9060-exec-90] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警模型列表!请求IP地址: ************** 操作开始时间: 2025-07-24 16:05:47 操作结束时间: 2025-07-24 16:05:47!总共花费时间: 53 毫秒！
2025-07-24 16:05:47.606 [] [21fbba0259429974/73ac1ed627e043d9] [http-nio-9060-exec-91] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 查询预警信息统计数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:05:47 操作结束时间: 2025-07-24 16:05:47!总共花费时间: 93 毫秒！
2025-07-24 16:05:48.726 [] [f64c8517792d586e/ed0fcfb7e0959531] [http-nio-9060-exec-92] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警明细数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:05:48 操作结束时间: 2025-07-24 16:05:48!总共花费时间: 65 毫秒！
2025-07-24 16:05:48.803 [] [08e2e6358408d8ac/946361d8363be1d9] [http-nio-9060-exec-94] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警明细数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:05:48 操作结束时间: 2025-07-24 16:05:48!总共花费时间: 65 毫秒！
2025-07-24 16:09:45.111 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 16:12:32.851 [] [379d20702817872b/6023a1a47977406f] [http-nio-9060-exec-95] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警看图详情!请求IP地址: ************** 操作开始时间: 2025-07-24 16:12:32 操作结束时间: 2025-07-24 16:12:32!总共花费时间: 247 毫秒！
2025-07-24 16:13:05.820 [] [ee0fb50ed0f175b0/13ed764f9594adde] [http-nio-9060-exec-97] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警看图详情!请求IP地址: ************** 操作开始时间: 2025-07-24 16:13:05 操作结束时间: 2025-07-24 16:13:05!总共花费时间: 100 毫秒！
2025-07-24 16:13:06.051 [] [38950cc3a85ac01b/110b0c4104cd2027] [http-nio-9060-exec-98] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取挂起状态信息!请求IP地址: ************** 操作开始时间: 2025-07-24 16:13:06 操作结束时间: 2025-07-24 16:13:06!总共花费时间: 47 毫秒！
2025-07-24 16:14:45.115 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 16:19:45.117 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 16:24:45.121 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 16:29:45.129 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 16:32:06.520 [OrganNo_00023_UserNo_admin] [d922716cefe4a492/39d291de876a4866] [http-nio-9060-exec-2] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"busi_data_date":"********",
			"model_id":"6",
			"model_row_id":"112"
		}
	],
	"sysMap":{
		
	}
}
2025-07-24 16:32:06.533 [OrganNo_00023_UserNo_admin] [d922716cefe4a492/362631166aeca95c] [http-nio-9060-exec-2] DEBUG c.s.a.r.d.a.A.getModelInfoById - ==>  Preparing: SELECT a.id as MODEL_ID, a.show_name as MODEL_NAME, a.privname as MODEL_PRIV, b.table_name as TABLE_NAME, a.model_check_way as MODEL_CHECK_WAY, a.model_data_check_way as MODEL_DATA_CHECK_WAY, a.relating_id as RELATING_MODEL_ID, a.model_type as MODEL_TYPE FROM MC_MODEL_TB a, MC_TABLE_TB b WHERE a.table_id = b.id AND a.id = ?
2025-07-24 16:32:06.534 [OrganNo_00023_UserNo_admin] [d922716cefe4a492/362631166aeca95c] [http-nio-9060-exec-2] DEBUG c.s.a.r.d.a.A.getModelInfoById - ==> Parameters: 6(String)
2025-07-24 16:32:06.537 [OrganNo_00023_UserNo_admin] [d922716cefe4a492/362631166aeca95c] [http-nio-9060-exec-2] DEBUG c.s.a.r.d.a.A.getModelInfoById - <==      Total: 1
2025-07-24 16:32:06.537 [OrganNo_00023_UserNo_admin] [d922716cefe4a492/362631166aeca95c] [http-nio-9060-exec-2] INFO  c.s.a.r.s.i.a.ArmsExhibitDataServiceImpl - 模型信息：{relating_model_id=5, model_priv=ARMS_BRASEXHIBIT, model_name=1003-账户挂失、解挂交易, model_type=1, model_id=6, model_check_way=0, table_name=ZD_1003_ZHANGHGSJG, model_data_check_way=0}
2025-07-24 16:32:06.537 [OrganNo_00023_UserNo_admin] [d922716cefe4a492/362631166aeca95c] [http-nio-9060-exec-2] DEBUG c.s.a.r.d.a.A.getRiskWarningDataForSlip - ==>  Preparing: SELECT * FROM ZD_1003_ZHANGHGSJG WHERE MODEL_ID = ? AND MODELROW_ID = ?
2025-07-24 16:32:06.538 [OrganNo_00023_UserNo_admin] [d922716cefe4a492/362631166aeca95c] [http-nio-9060-exec-2] DEBUG c.s.a.r.d.a.A.getRiskWarningDataForSlip - ==> Parameters: 6(String), 112(String)
2025-07-24 16:32:06.540 [OrganNo_00023_UserNo_admin] [d922716cefe4a492/362631166aeca95c] [http-nio-9060-exec-2] DEBUG c.s.a.r.d.a.A.getRiskWarningDataForSlip - <==      Total: 1
2025-07-24 16:32:06.540 [OrganNo_00023_UserNo_admin] [d922716cefe4a492/362631166aeca95c] [http-nio-9060-exec-2] INFO  c.s.a.r.s.i.a.ArmsExhibitDataServiceImpl - 业务数据字段：[resupervise_date, acc_sitename, form_type, former_loss_acc, hz_site_name, user_no, resupervise_time, site_no, resupervise_user, id_no, model_name, balance, resupervise_content, flow_id, ishandle, relate_modelid, create_date, data_file_name, hz_site_no, alert_time, create_time, fk_site_no, operator_no, occur_date, site_name, relate_modelrowid, auth_teller_no, busi_data_date, image_state, start_dealtime, br_cas_code, tran_site_handle, error_flag, alert_user, serial_no, field_content_cn, vouh_type, model_level, resupervise_username, tx_code, alert_username, loss_type, product_code, modelrow_id, loss_account_no, zbcl_flag, occur_time, alert_content, post_vouch_state, alert_date, id_type, pre_vouch_state, tx_name, hz_teller_name, form_id, model_id, zone_no, list_flag, operator_name, data_file_id, field1, model_lock, end_dealtime, field6, customer_name, field3, field2, vouh_no, field5, field4]
2025-07-24 16:32:06.540 [OrganNo_00023_UserNo_admin] [d922716cefe4a492/362631166aeca95c] [http-nio-9060-exec-2] INFO  c.s.a.r.s.i.a.ArmsExhibitDataServiceImpl - 业务数据内容：{resupervise_date=null, acc_sitename=中国银行珙县支行, form_type=null, former_loss_acc=null, hz_site_name=null, user_no=admin, resupervise_time=null, site_no=14902, resupervise_user=null, id_no=null, model_name=1003-账户挂失、解挂交易, balance=null, resupervise_content=null, flow_id=*********, ishandle=1, relate_modelid=null, create_date=********, data_file_name=00002, hz_site_no=null, alert_time=********094007, create_time=null, fk_site_no=15263, operator_no=1488420, occur_date=********, site_name=中国银行珙县支行, relate_modelrowid=null, auth_teller_no=null, busi_data_date=null, image_state=1, start_dealtime=null, br_cas_code=null, tran_site_handle=否, error_flag=null, alert_user=admin               , serial_no=null, field_content_cn=请查表核实, vouh_type=null, model_level=1, resupervise_username=null, tx_code=037223              , alert_username=系统超级管理员                             , loss_type=null, product_code=null, modelrow_id=112, loss_account_no=6217563100016683998, zbcl_flag=null, occur_time=10:48:35            , alert_content=1111, post_vouch_state=null, alert_date=********, id_type=null, pre_vouch_state=null, tx_name=销卡, hz_teller_name=null, form_id=null, model_id=6, zone_no=5101, list_flag=0, operator_name=null, data_file_id=null, field1=0, model_lock=0, end_dealtime=null, field6=null, customer_name=null, field3=null, field2=null, vouh_no=null, field5=null, field4=null}
2025-07-24 16:32:06.695 [OrganNo_00023_UserNo_admin] [d922716cefe4a492/39d291de876a4866] [http-nio-9060-exec-2] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"flowFields":[
			{
				"FIELD_FLOAT":"0",
				"FIELD_NAME":"FLOW_ID",
				"ELSE_NAME":"流水号"
			},
			{
				"FIELD_FLOAT":"0",
				"FIELD_NAME":"ACCOUNT",
				"ELSE_NAME":"借方账号"
			},
			{
				"FIELD_FLOAT":"0",
				"FIELD_NAME":"CUSTOMER_ID",
				"ELSE_NAME":"客户号"
			},
			{
				"FIELD_FLOAT":"0",
				"FIELD_NAME":"LOAN_NO",
				"ELSE_NAME":"借据编号"
			},
			{
				"FIELD_FLOAT":"0",
				"FIELD_NAME":"CLIENT_NAME",
				"ELSE_NAME":"借方户名"
			},
			{
				"FIELD_FLOAT":"2",
				"FIELD_NAME":"AMOUNT",
				"ELSE_NAME":"交易金额"
			},
			{
				"FIELD_FLOAT":"0",
				"FIELD_NAME":"OPP_ACCT",
				"ELSE_NAME":"贷方账号"
			},
			{
				"FIELD_FLOAT":"0",
				"FIELD_NAME":"OPP_CLIENT_NAME",
				"ELSE_NAME":"贷方户名"
			},
			{
				"FIELD_FLOAT":"0",
				"FIELD_NAME":"CD_FLAG",
				"ELSE_NAME":"借贷标志"
			},
			{
				"FIELD_FLOAT":"0",
				"FIELD_NAME":"SITE_NO",
				"ELSE_NAME":"交易机构"
			},
			{
				"FIELD_FLOAT":"0",
				"FIELD_NAME":"OPERATOR_NO",
				"ELSE_NAME":"柜员ID"
			},
			{
				"FIELD_FLOAT":"0",
				"FIELD_NAME":"GRANT_NO",
				"ELSE_NAME":"授权柜员ID"
			},
			{
				"FIELD_FLOAT":"0",
				"FIELD_NAME":"OCCUR_DATE",
				"ELSE_NAME":"业务日期"
			},
			{
				"FIELD_FLOAT":"0",
				"FIELD_NAME":"OCCUR_TIME",
				"ELSE_NAME":"业务发生时间"
			},
			{
				"FIELD_FLOAT":"0",
				"FIELD_NAME":"TX_CODE",
				"ELSE_NAME":"交易代码"
			},
			{
				"FIELD_FLOAT":"0",
				"FIELD_NAME":"CURRENCY_TYPE",
				"ELSE_NAME":"货币代号"
			},
			{
				"FIELD_FLOAT":"0",
				"FIELD_NAME":"CHECK_FLAG",
				"ELSE_NAME":"勾对标志"
			},
			{
				"FIELD_FLOAT":"0",
				"FIELD_NAME":"LSERIAL_NO",
				"ELSE_NAME":"勾对图像序号"
			},
			{
				"FIELD_FLOAT":"0",
				"FIELD_NAME":"SEQ_ID",
				"ELSE_NAME":"流水序号"
			}
		],
		"modelInfo":{
			"relating_model_id":5,
			"model_priv":"ARMS_BRASEXHIBIT",
			"model_name":"1003-账户挂失、解挂交易",
			"model_type":"1",
			"model_id":6,
			"model_check_way":"0",
			"table_name":"ZD_1003_ZHANGHGSJG",
			"model_data_check_way":"0"
		},
		"businessData":{
			"acc_sitename":"中国银行珙县支行",
			"user_no":"admin",
			"site_no":"14902",
			"model_name":"1003-账户挂失、解挂交易",
			"flow_id":"*********",
			"ishandle":"1",
			"create_date":"********",
			"data_file_name":"00002",
			"alert_time":"********094007",
			"fk_site_no":"15263",
			"operator_no":"1488420",
			"occur_date":"********",
			"site_name":"中国银行珙县支行",
			"image_state":"1",
			"tran_site_handle":"否",
			"alert_user":"admin               ",
			"field_content_cn":"请查表核实",
			"model_level":"1",
			"tx_code":"037223              ",
			"alert_username":"系统超级管理员                             ",
			"modelrow_id":112,
			"loss_account_no":"6217563100016683998",
			"occur_time":"10:48:35            ",
			"alert_content":"1111",
			"alert_date":"********",
			"tx_name":"销卡",
			"model_id":6,
			"zone_no":"5101",
			"list_flag":"0",
			"field1":"0",
			"model_lock":"0"
		},
		"batchList":[
			{
				"batchId":"********150114621932",
				"inputDate":"********",
				"occurDate":"********",
				"operatorNo":"1488420 - 童心",
				"siteNo":"14902 - 中国银行成都实业街支行",
				"contentId":"202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1"
			}
		],
		"tableName":"ZD_1003_ZHANGHGSJG",
		"tmpDataList":[
			{
				"serialNo":"3UNu5EYz5UBGPqAK8Bp",
				"batchId":"********150114621932",
				"inccodeinBatch":2,
				"copyInccodein":0,
				"copyRec":0,
				"isFrontPage":"0",
				"psLevel":"1",
				"primaryInccodein":0,
				"formName":"转账凭证",
				"checkFlag":"1",
				"errorFlag":"0",
				"isAudit":"0",
				"selfDelete":"0",
				"processState":"201000",
				"flowId":"*********",
				"imageSize":0,
				"backImageSize":0,
				"patchFlag":0,
				"fileName":"00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg",
				"backFileName":"00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-B.jpg",
				"ocrWorker":"admin",
				"ocrDate":"********",
				"ocrTime":"15:58:44",
				"superviseDeal":"0",
				"dataSourceId":"0",
				"insertDate":"********"
			}
		],
		"flowList":[
			{
				"amount":0.0,
				"tx_code":"029160",
				"operator_no":"1488420",
				"lserial_no":"3UNu5EYz5UBGPqAK8Bp",
				"occur_date":"********",
				"site_no":"14902",
				"flow_id":"*********",
				"check_flag":"1",
				"seq_id":389527,
				"account":"*****************"
			}
		]
	},
	"retMsg":"获取预警看图详情成功"
}
2025-07-24 16:32:06.695 [] [d922716cefe4a492/39d291de876a4866] [http-nio-9060-exec-2] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警看图详情!请求IP地址: ************** 操作开始时间: 2025-07-24 16:32:06 操作结束时间: 2025-07-24 16:32:06!总共花费时间: 200 毫秒！
2025-07-24 16:32:06.802 [] [fc252f750aa35caa/fb01787c369b2757] [http-nio-9060-exec-3] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取挂起状态信息!请求IP地址: ************** 操作开始时间: 2025-07-24 16:32:06 操作结束时间: 2025-07-24 16:32:06!总共花费时间: 37 毫秒！
2025-07-24 16:34:45.140 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 16:39:45.147 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 16:40:34.313 [] [43489b793f4ac8d2/42c97fc787dad1e4] [http-nio-9060-exec-5] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警看图详情!请求IP地址: ************** 操作开始时间: 2025-07-24 16:40:34 操作结束时间: 2025-07-24 16:40:34!总共花费时间: 74 毫秒！
2025-07-24 16:40:34.469 [] [1a1311588eed96fc/d675d6962c788a82] [http-nio-9060-exec-6] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取挂起状态信息!请求IP地址: ************** 操作开始时间: 2025-07-24 16:40:34 操作结束时间: 2025-07-24 16:40:34!总共花费时间: 58 毫秒！
2025-07-24 16:40:38.443 [] [e461e019a429d392/662b3d2f0e5c07d4] [http-nio-9060-exec-8] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警模型列表!请求IP地址: ************** 操作开始时间: 2025-07-24 16:40:38 操作结束时间: 2025-07-24 16:40:38!总共花费时间: 37 毫秒！
2025-07-24 16:40:38.550 [] [029d7b1fde426ccb/1cc2c0d3873916c1] [http-nio-9060-exec-9] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 查询预警信息统计数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:40:38 操作结束时间: 2025-07-24 16:40:38!总共花费时间: 92 毫秒！
2025-07-24 16:40:42.971 [] [d229f0003cf2d7cf/150e4dd67103d98a] [http-nio-9060-exec-11] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警明细数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:40:42 操作结束时间: 2025-07-24 16:40:42!总共花费时间: 58 毫秒！
2025-07-24 16:40:43.048 [] [b1c4eeff48add39a/deb0cd1b73a752cc] [http-nio-9060-exec-12] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警明细数据!请求IP地址: ************** 操作开始时间: 2025-07-24 16:40:42 操作结束时间: 2025-07-24 16:40:43!总共花费时间: 67 毫秒！
2025-07-24 16:44:45.147 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 16:49:45.149 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 16:54:45.164 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 16:55:16.501 [] [3d7705e847730900/bdd444aa6dd31eb4] [http-nio-9060-exec-14] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警看图详情!请求IP地址: ************** 操作开始时间: 2025-07-24 16:55:16 操作结束时间: 2025-07-24 16:55:16!总共花费时间: 100 毫秒！
2025-07-24 16:55:16.590 [] [2bd0b2e8b5b5f7b7/e73b7a2438ae6b5f] [http-nio-9060-exec-15] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取挂起状态信息!请求IP地址: ************** 操作开始时间: 2025-07-24 16:55:16 操作结束时间: 2025-07-24 16:55:16!总共花费时间: 23 毫秒！
2025-07-24 16:59:45.172 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 17:04:45.180 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 17:09:45.189 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 17:14:45.199 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 17:19:45.207 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 17:24:45.213 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 17:29:45.223 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 17:34:45.239 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 17:39:45.244 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 17:44:45.253 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 17:49:45.253 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 17:54:45.268 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 17:59:45.280 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 18:04:45.282 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 18:09:45.291 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 18:14:18.620 [OrganNo_00023_UserNo_admin] [432381c76ced2a7f/9a6531700fa839e6] [http-nio-9060-exec-17] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"getAllModelInfos"
	}
}
2025-07-24 18:14:18.637 [OrganNo_00023_UserNo_admin] [432381c76ced2a7f/5941ab1dff93dd7d] [http-nio-9060-exec-17] DEBUG c.s.a.r.d.a.A.getAllModelInfoDao - ==>  Preparing: SELECT a.id as MODEL_ID, a.show_name as MODEL_NAME, a.privname as MODEL_PRIV, b.table_name as TABLE_NAME, a.model_check_way as MODEL_CHECK_WAY, a.model_data_check_way as MODEL_DATA_CHECK_WAY, a.relating_id as RELATING_MODEL_ID, a.model_type as MODEL_TYPE, CASE WHEN EXISTS ( SELECT 1 FROM MC_MODEL_TB m WHERE m.relating_id = a.id ) THEN 1 ELSE 0 END as IS_RELATE_MODEL FROM MC_MODEL_TB a, MC_TABLE_TB b WHERE a.table_id = b.id AND a.status = 1 AND (a.model_type = 0 OR a.model_type = 1) ORDER BY a.name
2025-07-24 18:14:18.638 [OrganNo_00023_UserNo_admin] [432381c76ced2a7f/5941ab1dff93dd7d] [http-nio-9060-exec-17] DEBUG c.s.a.r.d.a.A.getAllModelInfoDao - ==> Parameters: 
2025-07-24 18:14:18.644 [OrganNo_00023_UserNo_admin] [432381c76ced2a7f/5941ab1dff93dd7d] [http-nio-9060-exec-17] DEBUG c.s.a.r.d.a.A.getAllModelInfoDao - <==      Total: 52
2025-07-24 18:14:18.661 [OrganNo_00023_UserNo_admin] [432381c76ced2a7f/9a6531700fa839e6] [http-nio-9060-exec-17] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"modelList":[
			{
				"relating_model_id":1,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1001-错账冲正交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":2,
				"model_check_way":"0",
				"table_name":"ZD_1001_CUOZCZJY",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1001-错账冲正交易(明细)",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":1,
				"model_check_way":"0",
				"table_name":"ZD_1001_CUOZCZJY",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":3,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1002-账户冻结解冻交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":4,
				"model_check_way":"0",
				"table_name":"ZD_1002_ZHANGHDJJD",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1002-账户冻结解冻交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":3,
				"model_check_way":"0",
				"table_name":"ZD_1002_ZHANGHDJJD",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":5,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1003-账户挂失、解挂交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":6,
				"model_check_way":"0",
				"table_name":"ZD_1003_ZHANGHGSJG",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1003-账户挂失、解挂交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":5,
				"model_check_way":"0",
				"table_name":"ZD_1003_ZHANGHGSJG",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":7,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1004-特殊汇率交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":8,
				"model_check_way":"0",
				"table_name":"ZD_1004_TESHL",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1004-特殊汇率交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":7,
				"model_check_way":"0",
				"table_name":"ZD_1004_TESHL",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":9,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1005-强制支取",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":10,
				"model_check_way":"0",
				"table_name":"ZD_1005_QIANGZZQ",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1005-强制支取（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":9,
				"model_check_way":"0",
				"table_name":"ZD_1005_QIANGZZQ",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":11,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1006-非当日起息交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":12,
				"model_check_way":"0",
				"table_name":"ZD_1006_FEIDRQX",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1006-非当日起息交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":11,
				"model_check_way":"0",
				"table_name":"ZD_1006_FEIDRQX",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":13,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1007-存、贷款调整交易、手工强制结息交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":14,
				"model_check_way":"0",
				"table_name":"ZD_1007_CUNDKTZSGQZJX",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1007-存、贷款调整交易、手工强制结息交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":13,
				"model_check_way":"0",
				"table_name":"ZD_1007_CUNDKTZSGQZJX",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":15,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1008-存款账户利率维护/变更交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":16,
				"model_check_way":"0",
				"table_name":"ZD_1008_CUNKZHLLWH",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1008-存款账户利率维护/变更交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":15,
				"model_check_way":"0",
				"table_name":"ZD_1008_CUNKZHLLWH",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":17,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1009-存款、贷款账户换机构交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":18,
				"model_check_way":"0",
				"table_name":"ZD_1009_CUNKDKZHHJG",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1009-存款、贷款账户换机构交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":17,
				"model_check_way":"0",
				"table_name":"ZD_1009_CUNKDKZHHJG",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":19,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1010-重空状态变更交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":20,
				"model_check_way":"0",
				"table_name":"ZD_1010_ZHONGKZTBG",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1010-重空状态变更交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":19,
				"model_check_way":"0",
				"table_name":"ZD_1010_ZHONGKZTBG",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":21,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1011-5111人民币长期不动户支取",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":22,
				"model_check_way":"0",
				"table_name":"ZD_1011_CHANGQWJFHKJK",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1011-5111人民币长期不动户支取（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":21,
				"model_check_way":"0",
				"table_name":"ZD_1011_CHANGQWJFHKJK",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":23,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1012-大额手续费收入及支出检查",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":24,
				"model_check_way":"0",
				"table_name":"ZD_1012_SHOUXFSRJC",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1012-大额手续费收入及支出检查（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":23,
				"model_check_way":"0",
				"table_name":"ZD_1012_SHOUXFSRJC",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":25,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1013-741挂账、842销账、8429支取交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":26,
				"model_check_way":"0",
				"table_name":"ZD_1013_GUAZXZZQ",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1013-741挂账、842销账、8429支取交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":25,
				"model_check_way":"0",
				"table_name":"ZD_1013_GUAZXZZQ",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":27,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1014-单位大额交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":28,
				"model_check_way":"0",
				"table_name":"ZD_1014_DANWDEJY",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1014-单位大额交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":27,
				"model_check_way":"0",
				"table_name":"ZD_1014_DANWDEJY",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":29,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1015-不同客户新开立借记卡预留相同手机号",
				"is_relate_model":0,
				"model_type":"0",
				"model_id":30,
				"model_check_way":"0",
				"table_name":"YJ_1001_XINKDZHSJXT",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1015-不同客户新开立借记卡预留相同手机号（明细）",
				"is_relate_model":1,
				"model_type":"0",
				"model_id":29,
				"model_check_way":"0",
				"table_name":"YJ_1001_XINKDZHSJXT",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":31,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1016-验资（增资）账户冻结情况检查",
				"is_relate_model":0,
				"model_type":"0",
				"model_id":32,
				"model_check_way":"0",
				"table_name":"YJ_1002_YANZZZZH",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1016-验资（增资）账户冻结情况检查（明细）",
				"is_relate_model":1,
				"model_type":"0",
				"model_id":31,
				"model_check_way":"0",
				"table_name":"YJ_1002_YANZZZZH",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":33,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1017-非营业时间（19时后）交易",
				"is_relate_model":0,
				"model_type":"0",
				"model_id":34,
				"model_check_way":"0",
				"table_name":"YJ_1003_FEIYYSJJY",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1017-非营业时间（19时后）交易（明细）",
				"is_relate_model":1,
				"model_type":"0",
				"model_id":33,
				"model_check_way":"0",
				"table_name":"YJ_1003_FEIYYSJJY",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":36,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1018-经集中核准的人民币大额交易 ",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":35,
				"model_check_way":"0",
				"table_name":"HZ_1001_RMBDEJY",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBI",
				"model_name":"1018-经集中核准的人民币大额交易(明细)  ",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":36,
				"model_check_way":"0",
				"table_name":"HZ_1001_RMBDEJY",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":38,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1019-经集中核准的外币大额交易 ",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":37,
				"model_check_way":"0",
				"table_name":"HZ_1002_WBDEJY",
				"model_data_check_way":"1"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1019-经集中核准的外币大额交易(明细)",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":38,
				"model_check_way":"0",
				"table_name":"HZ_1002_WBDEJY",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":40,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1020-经集中核准的司法扣划交易 ",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":39,
				"model_check_way":"0",
				"table_name":"HZ_1003_SFKHJY",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBI",
				"model_name":"1020-经集中核准的司法扣划交易(明细)",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":40,
				"model_check_way":"0",
				"table_name":"HZ_1003_SFKHJY",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":42,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1021-经集中核准的司法冻结、解冻交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":41,
				"model_check_way":"0",
				"table_name":"HZ_1004_SFDJJDJY",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1021-经集中核准的司法冻结、解冻交易(明细)",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":42,
				"model_check_way":"0",
				"table_name":"HZ_1004_SFDJJDJY",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":44,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1022-经集中核准的挂失、解挂交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":43,
				"model_check_way":"0",
				"table_name":"HZ_1005_GSJGJY",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1022-经集中核准的挂失、解挂交易(明细) ",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":44,
				"model_check_way":"0",
				"table_name":"HZ_1005_GSJGJY",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":46,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1023-存款利率调整成功模型",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":45,
				"model_check_way":"0",
				"table_name":"CSAR7010",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1023-存款利率调整成功模型(明细) ",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":46,
				"model_check_way":"0",
				"table_name":"CSAR7010",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":48,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1024-贷款利率调整成功模型",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":47,
				"model_check_way":"0",
				"table_name":"CSAR7020",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1024-贷款利率调整成功模型(明细) ",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":48,
				"model_check_way":"0",
				"table_name":"CSAR7020",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":50,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1025-特殊业务调整成功模型",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":49,
				"model_check_way":"0",
				"table_name":"CSAR7040",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1025-特殊业务调整成功模型(明细) ",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":50,
				"model_check_way":"0",
				"table_name":"CSAR7040",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":52,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1026-GLIF调整成功模型",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":51,
				"model_check_way":"0",
				"table_name":"CSAR7050",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1026-GLIF调整成功模型(明细) ",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":52,
				"model_check_way":"0",
				"table_name":"CSAR7050",
				"model_data_check_way":"0"
			}
		]
	},
	"retMsg":"查询成功"
}
2025-07-24 18:14:18.662 [] [432381c76ced2a7f/9a6531700fa839e6] [http-nio-9060-exec-17] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警模型列表!请求IP地址: ************** 操作开始时间: 2025-07-24 18:14:18 操作结束时间: 2025-07-24 18:14:18!总共花费时间: 51 毫秒！
2025-07-24 18:14:45.305 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 18:19:45.321 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 18:24:47.428 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 18:29:47.431 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 18:34:47.438 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 18:39:47.454 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 18:44:47.467 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
